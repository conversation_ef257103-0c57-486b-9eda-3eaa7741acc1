import 'package:flutter/material.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/core/widgets/svg_icon.dart';
import 'package:shop_app/gen/assets.gen.dart';

class AuthCard extends StatelessWidget {
  final Widget card;

  const AuthCard({super.key, required this.card});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          Theme.of(context).primaryColor, // 👈 Important for contrast
      appBar: AppBar(
        backgroundColor: Theme.of(context).primaryColor,
        toolbarHeight: MediaQuery.of(context).size.height * 0.15,
        centerTitle: true,
        automaticallyImplyLeading: false,
        title: SvgIcon(
          icon: Assets.icons.logo,
          color: StyleRepo.white,
          size: 120,
        ),
        // elevation: 0,
      ),
      body: Container(
        padding: EdgeInsets.symmetric(horizontal: 15),
        decoration: BoxDecoration(
          color: StyleRepo.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(40),
            topRight: Radius.circular(40),
          ),
        ),
        child: Stack(
          children: [Assets.icons.authBackgraound.svg(), card],
        ),
      ),
    );
  }
}
