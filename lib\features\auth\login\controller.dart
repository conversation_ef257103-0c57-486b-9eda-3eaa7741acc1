import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/config/app_builder.dart';
import 'package:shop_app/core/config/role.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/routes/routes.dart';
import 'package:shop_app/core/services/rest_api/constants/end_points.dart';
import 'package:shop_app/core/services/rest_api/models/request.dart';
import 'package:shop_app/core/services/rest_api/models/response_model.dart';
import '../../../core/services/rest_api/api_service.dart';

class LoginPageController extends GetxController {
  AppBuilder appBuilder = Get.find();
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  late TextEditingController userName, password;

  @override
  onInit() {
    userName = TextEditingController();
    password = TextEditingController();
    super.onInit();
  }

  @override
  onClose() {
    userName.dispose();
    password.dispose();
    super.onClose();
  }

  confirm() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.login,
        method: RequestMethod.Post,
        body: {
          "name": userName.text,
          "password": password.text,
        },
      ),
    );
    if (response.success) {
      appBuilder.setToken(response.data['access_token']);
      appBuilder.setRole(Role.user);
      if (response.data['type'] == "driver") {
        appBuilder.setIsDelivery(true);
        Get.offNamed(Pages.MyOrders.value, arguments: true);
      } else {
        appBuilder.setIsDelivery(false);
        Get.offNamed(Pages.home.value, arguments: false);
      }
    } else {
      Get.snackbar(LocaleKeys.error, LocaleKeys.Wrong_email);
    }
  }
}
