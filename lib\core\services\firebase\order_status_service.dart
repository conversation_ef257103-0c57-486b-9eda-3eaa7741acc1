// import 'dart:convert';
// import 'dart:developer';
// import 'package:get/get.dart';
// import 'package:shop_app/core/config/StatusOrder.dart';
// import 'package:shop_app/core/services/firebase/notification_service.dart';
// import 'package:shop_app/features/MyOrders/Model/OderModel.dart';

// class OrderStatusService extends GetxService {
//   static OrderStatusService get instance => Get.find<OrderStatusService>();

//   final NotificationService _notificationService = NotificationService.instance;

//   // قائمة الطلبات المراقبة
//   final RxList<OrderModel> _trackedOrders = <OrderModel>[].obs;
//   List<OrderModel> get trackedOrders => _trackedOrders;

//   @override
//   Future<void> onInit() async {
//     super.onInit();
//     await _initializeOrderTracking();
//   }

//   // تهيئة مراقبة الطلبات
//   Future<void> _initializeOrderTracking() async {
//     try {
//       // الاشتراك في موضوع إشعارات الطلبات
//       await _notificationService.subscribeToTopic('order_updates');
//       log('تم الاشتراك في إشعارات الطلبات');
//     } catch (e) {
//       log('خطأ في تهيئة مراقبة الطلبات: $e');
//     }
//   }

//   // إضافة طلب للمراقبة
//   void trackOrder(OrderModel order) {
//     if (!_trackedOrders.any((o) => o.id == order.id)) {
//       _trackedOrders.add(order);
//       log('تم إضافة الطلب ${order.id} للمراقبة');
//     }
//   }

//   // إزالة طلب من المراقبة
//   void untrackOrder(int orderId) {
//     _trackedOrders.removeWhere((order) => order.id == orderId);
//     log('تم إزالة الطلب $orderId من المراقبة');
//   }

//   // تحديث حالة الطلب
//   void updateOrderStatus(int orderId, OrderStatus newStatus) {
//     int index = _trackedOrders.indexWhere((order) => order.id == orderId);
//     if (index != -1) {
//       OrderModel oldOrder = _trackedOrders[index];
//       OrderStatus oldStatus = OrderStatusParser.fromString(oldOrder.status);

//       // تحديث الحالة
//       _trackedOrders[index] = OrderModel(
//         id: oldOrder.id,
//         totalPrice: oldOrder.totalPrice,
//         status: newStatus.toApiString(),
//         address: oldOrder.address,
//         createdAt: oldOrder.createdAt,
//         items: oldOrder.items,
//         customer: oldOrder.customer,
//       );

//       // إرسال إشعار بتغيير الحالة
//       _sendOrderStatusNotification(orderId, oldStatus, newStatus);

//       log('تم تحديث حالة الطلب $orderId من ${oldStatus.label} إلى ${newStatus.label}');
//     }
//   }

//   // إرسال إشعار تغيير حالة الطلب
//   void _sendOrderStatusNotification(
//       int orderId, OrderStatus oldStatus, OrderStatus newStatus) {
//     String title = _getStatusChangeTitle(newStatus);
//     String body = _getStatusChangeBody(orderId, oldStatus, newStatus);

//     // إنشاء إشعار محلي
//     NotificationModel notification = NotificationModel(
//       id: 'order_${orderId}_${DateTime.now().millisecondsSinceEpoch}',
//       title: title,
//       body: body,
//       data: {
//         'type': 'order_status',
//         'order_id': orderId.toString(),
//         'old_status': oldStatus.toApiString(),
//         'new_status': newStatus.toApiString(),
//       },
//       timestamp: DateTime.now(),
//       type: NotificationType.orderUpdate,
//     );

//     // إضافة الإشعار للقائمة
//     _notificationService.addNotification(notification);

//     // عرض إشعار فوري
//     _notificationService.showLocalNotification(notification);
//   }

//   // الحصول على عنوان إشعار تغيير الحالة
//   String _getStatusChangeTitle(OrderStatus status) {
//     switch (status) {
//       case OrderStatus.pending:
//         return 'تم استلام طلبك';
//       case OrderStatus.processing:
//         return 'جاري تحضير طلبك';
//       case OrderStatus.outForDelivery:
//         return 'طلبك في الطريق إليك';
//       case OrderStatus.completed:
//         return 'تم تسليم طلبك';
//       case OrderStatus.cancelled:
//         return 'تم إلغاء طلبك';
//       case OrderStatus.rejected:
//         return 'تم رفض طلبك';
//     }
//   }

//   // الحصول على نص إشعار تغيير الحالة
//   String _getStatusChangeBody(
//       int orderId, OrderStatus oldStatus, OrderStatus newStatus) {
//     switch (newStatus) {
//       case OrderStatus.pending:
//         return 'طلبك رقم #$orderId قيد المراجعة وسيتم تحضيره قريباً';
//       case OrderStatus.processing:
//         return 'طلبك رقم #$orderId قيد التحضير الآن';
//       case OrderStatus.outForDelivery:
//         return 'طلبك رقم #$orderId خرج للتوصيل وسيصل إليك قريباً';
//       case OrderStatus.completed:
//         return 'طلبك رقم #$orderId تم تسليمه بنجاح. نشكرك لاختيارك متجرنا!';
//       case OrderStatus.cancelled:
//         return 'طلبك رقم #$orderId تم إلغاؤه. يمكنك التواصل معنا للمزيد من التفاصيل';
//       case OrderStatus.rejected:
//         return 'طلبك رقم #$orderId تم رفضه. يمكنك التواصل معنا للمزيد من التفاصيل';
//     }
//   }

//   // محاكاة تحديث حالة الطلب (للاختبار)
//   void simulateOrderStatusUpdate(int orderId) {
//     int index = _trackedOrders.indexWhere((order) => order.id == orderId);
//     if (index == -1) return;

//     OrderModel order = _trackedOrders[index];
//     OrderStatus currentStatus = OrderStatusParser.fromString(order.status);
//     OrderStatus? nextStatus = _getNextStatus(currentStatus);

//     if (nextStatus != null) {
//       updateOrderStatus(orderId, nextStatus);
//     }
//   }

//   // الحصول على الحالة التالية للطلب
//   OrderStatus? _getNextStatus(OrderStatus currentStatus) {
//     switch (currentStatus) {
//       case OrderStatus.pending:
//         return OrderStatus.processing;
//       case OrderStatus.processing:
//         return OrderStatus.outForDelivery;
//       case OrderStatus.outForDelivery:
//         return OrderStatus.completed;
//       case OrderStatus.completed:
//       case OrderStatus.cancelled:
//       case OrderStatus.rejected:
//         return null; // لا توجد حالة تالية
//     }
//   }

//   // الحصول على طلب بالمعرف
//   OrderModel? getOrderById(int orderId) {
//     try {
//       return _trackedOrders.firstWhere((order) => order.id == orderId);
//     } catch (e) {
//       return null;
//     }
//   }

//   // الحصول على الطلبات حسب الحالة
//   List<OrderModel> getOrdersByStatus(OrderStatus status) {
//     return _trackedOrders
//         .where((order) => OrderStatusParser.fromString(order.status) == status)
//         .toList();
//   }

//   // الحصول على الطلبات النشطة (غير المكتملة أو الملغاة)
//   List<OrderModel> getActiveOrders() {
//     return _trackedOrders.where((order) {
//       OrderStatus status = OrderStatusParser.fromString(order.status);
//       return status != OrderStatus.completed &&
//           status != OrderStatus.cancelled &&
//           status != OrderStatus.rejected;
//     }).toList();
//   }

//   // مسح جميع الطلبات المراقبة
//   void clearTrackedOrders() {
//     _trackedOrders.clear();
//     log('تم مسح جميع الطلبات المراقبة');
//   }

//   // إلغاء الاشتراك في إشعارات الطلبات
//   Future<void> unsubscribeFromOrderUpdates() async {
//     try {
//       await _notificationService.unsubscribeFromTopic('order_updates');
//       log('تم إلغاء الاشتراك في إشعارات الطلبات');
//     } catch (e) {
//       log('خطأ في إلغاء الاشتراك: $e');
//     }
//   }

//   @override
//   void onClose() {
//     unsubscribeFromOrderUpdates();
//     super.onClose();
//   }
// }
