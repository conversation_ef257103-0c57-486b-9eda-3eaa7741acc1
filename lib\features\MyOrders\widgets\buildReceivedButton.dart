import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/style/repo.dart';

class buildReceivedButton extends StatelessWidget {
  const buildReceivedButton({
    super.key,
    required this.onMarkAsReceived,
    required this.orderNumber,
  });

  final Function(int orderNumber)? onMarkAsReceived;
  final int orderNumber;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 50,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [StyleRepo.green, StyleRepo.green.withValues(alpha: 0.8)],
        ),
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: StyleRepo.green.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: () async {
          if (onMarkAsReceived != null) {
            await onMarkAsReceived!(orderNumber);
          }
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle_outline,
              color: StyleRepo.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              tr(LocaleKeys.completed),
              style: TextStyle(
                color: StyleRepo.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
