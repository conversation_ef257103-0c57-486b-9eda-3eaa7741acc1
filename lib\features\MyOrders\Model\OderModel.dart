import 'dart:convert';

class OrderModel {
  late int id;
  late double totalPrice;
  late String status;
  late String address;
  late DateTime createdAt;
  DateTime? deliveryTime;
  late List<ProductsOrder> items;
  Customer? customer; // اجعلها nullable
  String? notes;
  OrderModel(
      {required this.id,
      required this.totalPrice,
      required this.status,
      required this.address,
      required this.createdAt,
      required this.items,
      this.customer,
      this.deliveryTime,
      this.notes});

  OrderModel.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    totalPrice = (json["totalPrice"] as num?)?.toDouble() ?? 0.0;
    status = json["status"];
    address = json["address"];
    createdAt = DateTime.parse(json["createdAt"]);
    if (json.containsKey("deliveryTime") && json["deliveryTime"] != null) {
      deliveryTime = DateTime.parse(json["deliveryTime"]);
    }
    items = List<ProductsOrder>.from(
      json["ProductsOrder"].map(
        (x) => ProductsOrder.fromJson(x),
      ),
    );
    if (json.containsKey("customer") && json["customer"] != null) {
      customer = Customer.fromJson(json["customer"]);
    }
    notes = json["notes"];
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "totalPrice": totalPrice,
        "status": status,
        "address": address,
        "createdAt": createdAt.toIso8601String(),
        if (deliveryTime != null)
          "deliveryTime": deliveryTime!.toIso8601String(),
        "ProductsOrder": items.map((x) => x.toJson()).toList(),
        if (customer != null) "customer": customer!.toJson(),
        "notes": notes,
      };
}

class ProductsOrder {
  int id;
  String name;
  String category;
  int quantity;
  double price;
  double totalPrice;
  String? notes;

  ProductsOrder({
    required this.id,
    required this.name,
    required this.category,
    required this.quantity,
    required this.price,
    required this.totalPrice,
    this.notes,
  });

  factory ProductsOrder.fromRawJson(String str) =>
      ProductsOrder.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ProductsOrder.fromJson(Map<String, dynamic> json) => ProductsOrder(
        id: json["id"],
        name: json["name"],
        category: json["category"],
        quantity: json["quantity"],
        price: (json["price"] as num?)?.toDouble() ?? 0.0,
        totalPrice: (json["totalPrice"] as num?)?.toDouble() ?? 0.0,
        notes: json["notes"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "category": category,
        "quantity": quantity,
        "price": price,
        "totalPrice": totalPrice,
        "notes": notes,
      };
}

class Customer {
  int id;
  String name;
  String phoneNumber;
  double latitude;
  double longitude;

  Customer({
    required this.id,
    required this.name,
    required this.phoneNumber,
    required this.latitude,
    required this.longitude,
  });

  factory Customer.fromRawJson(String str) =>
      Customer.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Customer.fromJson(Map<String, dynamic> json) => Customer(
        id: json["id"],
        name: json["name"],
        phoneNumber: json["phoneNumber"],
        latitude: json["latitude"]?.toDouble(),
        longitude: json["longitude"]?.toDouble(),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "phoneNumber": phoneNumber,
        "latitude": latitude,
        "longitude": longitude,
      };
}
