import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/config/StatusOrder.dart';
import 'package:shop_app/core/services/firebase/notification_service.dart';
import 'package:shop_app/core/services/firebase/order_status_service.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/features/MyOrders/Model/OderModel.dart';

class TestNotificationsWidget extends StatelessWidget {
  const TestNotificationsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final notificationService = NotificationService.instance;
    final orderStatusService = OrderStatusService.instance;

    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: StyleRepo.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: StyleRepo.lightGrey),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'اختبار الإشعارات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: StyleRepo.black,
            ),
          ),
          const SizedBox(height: 16),

          // إنشاء طلب تجريبي
          ElevatedButton.icon(
            onPressed: () => _createTestOrder(orderStatusService),
            icon: const Icon(Icons.add_shopping_cart),
            label: const Text('إنشاء طلب تجريبي'),
            style: ElevatedButton.styleFrom(
              backgroundColor: StyleRepo.blue,
              foregroundColor: StyleRepo.white,
            ),
          ),

          const SizedBox(height: 8),

          // محاكاة تحديث حالة الطلب
          ElevatedButton.icon(
            onPressed: () => _simulateOrderUpdate(orderStatusService),
            icon: const Icon(Icons.update),
            label: const Text('محاكاة تحديث الطلب'),
            style: ElevatedButton.styleFrom(
              backgroundColor: StyleRepo.orange,
              foregroundColor: StyleRepo.white,
            ),
          ),

          const SizedBox(height: 8),

          // إرسال إشعار عرض
          ElevatedButton.icon(
            onPressed: () => _sendPromotionNotification(notificationService),
            icon: const Icon(Icons.local_offer),
            label: const Text('إرسال إشعار عرض'),
            style: ElevatedButton.styleFrom(
              backgroundColor: StyleRepo.green,
              foregroundColor: StyleRepo.white,
            ),
          ),

          const SizedBox(height: 8),

          // إرسال إشعار عام
          ElevatedButton.icon(
            onPressed: () => _sendGeneralNotification(notificationService),
            icon: const Icon(Icons.notifications),
            label: const Text('إرسال إشعار عام'),
            style: ElevatedButton.styleFrom(
              backgroundColor: StyleRepo.indigo,
              foregroundColor: StyleRepo.white,
            ),
          ),

          const SizedBox(height: 16),

          // عرض عدد الإشعارات
          Obx(() {
            final unreadCount = notificationService.unreadCount.value;
            final totalCount = notificationService.notifications.length;

            return Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: StyleRepo.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: StyleRepo.blue),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'إجمالي الإشعارات: $totalCount\nغير المقروءة: $unreadCount',
                      style: TextStyle(
                        fontSize: 14,
                        color: StyleRepo.blue,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  void _createTestOrder(OrderStatusService orderStatusService) {
    final testOrder = OrderModel(
      id: DateTime.now().millisecondsSinceEpoch,
      totalPrice: 150,
      status: OrderStatus.pending.toApiString(),
      address: 'عنوان تجريبي - الرياض، المملكة العربية السعودية',
      createdAt: DateTime.now(),
      items: [
        ProductsOrder(
          id: 1,
          name: 'منتج تجريبي',
          price: 50,
          quantity: 3,
          totalPrice: 150,
          category: 'TestCategory',
        ),
      ],
    );

    orderStatusService.trackOrder(testOrder);

    Get.snackbar(
      'تم إنشاء الطلب',
      'تم إنشاء طلب تجريبي برقم ${testOrder.id}',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: StyleRepo.green.withOpacity(0.9),
      colorText: StyleRepo.white,
    );
  }

  void _simulateOrderUpdate(OrderStatusService orderStatusService) {
    final activeOrders = orderStatusService.getActiveOrders();

    if (activeOrders.isEmpty) {
      Get.snackbar(
        'لا توجد طلبات',
        'يرجى إنشاء طلب تجريبي أولاً',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: StyleRepo.orange.withValues(alpha: 0.9),
        colorText: StyleRepo.white,
      );
      return;
    }

    final order = activeOrders.first;
    orderStatusService.simulateOrderStatusUpdate(order.id);
  }

  void _sendPromotionNotification(NotificationService notificationService) {
    final notification = NotificationModel(
      id: 'promo_${DateTime.now().millisecondsSinceEpoch}',
      title: 'عرض خاص! خصم 50%',
      body: 'احصل على خصم 50% على جميع المنتجات. العرض محدود لفترة قصيرة!',
      data: {
        'type': 'promotion',
        'discount': '50',
        'category': 'all',
      },
      timestamp: DateTime.now(),
      type: NotificationType.promotion,
    );

    // إضافة الإشعار
    notificationService.addNotification(notification);

    // عرض إشعار فوري
    Get.snackbar(
      notification.title,
      notification.body,
      snackPosition: SnackPosition.TOP,
      backgroundColor: StyleRepo.orange.withValues(alpha: 0.9),
      colorText: StyleRepo.white,
      duration: const Duration(seconds: 4),
      margin: const EdgeInsets.all(16),
      borderRadius: 12,
      icon: const Icon(Icons.local_offer, color: Colors.white),
    );
  }

  void _sendGeneralNotification(NotificationService notificationService) {
    final notification = NotificationModel(
      id: 'general_${DateTime.now().millisecondsSinceEpoch}',
      title: 'مرحباً بك في متجرنا!',
      body: 'نشكرك لاستخدام تطبيقنا. نتمنى لك تجربة تسوق ممتعة.',
      data: {
        'type': 'general',
        'action': 'welcome',
      },
      timestamp: DateTime.now(),
      type: NotificationType.general,
    );

    // إضافة الإشعار
    notificationService.addNotification(notification);

    // عرض إشعار فوري
    Get.snackbar(
      notification.title,
      notification.body,
      snackPosition: SnackPosition.TOP,
      backgroundColor: StyleRepo.indigo.withValues(alpha: 0.9),
      colorText: StyleRepo.white,
      duration: const Duration(seconds: 4),
      margin: const EdgeInsets.all(16),
      borderRadius: 12,
      icon: const Icon(Icons.notifications, color: Colors.white),
    );
  }
}
