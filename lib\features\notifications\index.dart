import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/services/firebase/notification_service.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/features/notifications/controller.dart';
import 'package:shop_app/features/notifications/widgets/notification_card.dart';
import 'package:shop_app/features/notifications/widgets/test_notifications.dart';
import 'package:shop_app/gen/assets.gen.dart';

class NotificationsPage extends StatelessWidget {
  const NotificationsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(NotificationsController());

    return Scaffold(
      backgroundColor: StyleRepo.white,
      appBar: AppBar(
        backgroundColor: StyleRepo.white,
        elevation: 0,
        automaticallyImplyLeading: false,
        leading: IconButton(
          onPressed: () => Get.back(),
          icon: Assets.icons.back.svg(
            colorFilter: ColorFilter.mode(StyleRepo.black, BlendMode.srcIn),
          ),
          iconSize: 24,
        ),
        title: Text(
          tr(LocaleKeys.Notifications),
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: StyleRepo.black,
                fontWeight: FontWeight.bold,
              ),
        ),
        centerTitle: true,
        actions: [
          // زر تحديد الكل كمقروء
          Obx(() {
            final hasUnread =
                controller.notificationService.unreadCount.value > 0;
            return hasUnread
                ? IconButton(
                    onPressed: controller.markAllAsRead,
                    icon: Icon(
                      Icons.done_all,
                      color: StyleRepo.blue,
                    ),
                    tooltip: 'تحديد الكل كمقروء',
                  )
                : const SizedBox.shrink();
          }),
          // قائمة الخيارات
          PopupMenuButton<String>(
            icon: Icon(Icons.more_vert, color: StyleRepo.black),
            onSelected: (value) {
              switch (value) {
                case 'mark_all_read':
                  controller.markAllAsRead();
                  break;
                case 'clear_all':
                  controller.showClearAllDialog();
                  break;
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'mark_all_read',
                child: Row(
                  children: [
                    Icon(Icons.done_all, color: StyleRepo.blue),
                    const SizedBox(width: 8),
                    const Text('تحديد الكل كمقروء'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'clear_all',
                child: Row(
                  children: [
                    Icon(Icons.clear_all, color: StyleRepo.red),
                    const SizedBox(width: 8),
                    const Text('مسح جميع الإشعارات'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              StyleRepo.blue.withValues(alpha: 0.05),
              StyleRepo.white,
            ],
          ),
        ),
        child: Obx(() {
          final notifications = controller.notificationService.notifications;

          if (notifications.isEmpty) {
            return Column(
              children: [
                // ويدجت الاختبار (في وضع التطوير فقط)
                const TestNotificationsWidget(),
                Expanded(child: _buildEmptyState()),
              ],
            );
          }

          return RefreshIndicator(
            onRefresh: controller.refreshNotifications,
            color: StyleRepo.blue,
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: notifications.length + 1, // +1 لويدجت الاختبار
              itemBuilder: (context, index) {
                if (index == 0) {
                  // ويدجت الاختبار في الأعلى
                  return const TestNotificationsWidget();
                }

                final notification = notifications[index - 1];
                return NotificationCard(
                  notification: notification,
                  onTap: () => controller.onNotificationTap(notification),
                  onDelete: () =>
                      controller.deleteNotification(notification.id),
                );
              },
            ),
          );
        }),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: StyleRepo.blue.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.notifications_none_rounded,
              size: 60,
              color: StyleRepo.blue.withValues(alpha: 0.6),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد إشعارات',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: StyleRepo.darkGrey,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ستظهر إشعاراتك هنا عند وصولها',
            style: TextStyle(
              fontSize: 14,
              color: StyleRepo.lightGrey,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.arrow_back),
            label: const Text('العودة للرئيسية'),
            style: ElevatedButton.styleFrom(
              backgroundColor: StyleRepo.blue,
              foregroundColor: StyleRepo.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
