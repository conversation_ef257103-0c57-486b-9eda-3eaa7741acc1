import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:shop_app/core/services/rest_api/rest_api.dart';

class LocationDetailsController extends GetxController {
  // Text Controllers for additional details
  late TextEditingController NotesAddress;

  // Observable variables
  late String address = '';
  LatLng location = LatLng(0.0, 0.0);
  @override
  void onInit() {
    NotesAddress = TextEditingController();
    final arguments = Get.arguments as Map<String, dynamic>;
    address = arguments['address'];
    location = arguments['location'];
    super.onInit();
  }

  sendLocationDetails() async {
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.profile,
        method: RequestMethod.Put,
        body: {
          "latitude": location.latitude,
          "longitude": location.longitude,
          "NotesAddress": NotesAddress.text,
        },
      ),
    );

    if (response.success) {
    } else {}
  }

  @override
  void onClose() {
    NotesAddress.dispose();
    super.onClose();
  }
}
