import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/features/Account/controller.dart';
import 'package:shop_app/features/Account/widgets/ChangeLanguage.dart';
import 'package:shop_app/features/Account/widgets/MenuCards.dart';
import 'package:shop_app/features/Account/widgets/ProfileView.dart';
import 'package:shop_app/gen/assets.gen.dart';

class AccountPage extends StatelessWidget {
  const AccountPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(AccountPageController());
    return Container(
      height: MediaQuery.of(context).size.height,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            StyleRepo.gray,
            StyleRepo.white,
          ],
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          children: [
            const Sized<PERSON>ox(height: 50),
            Profile<PERSON>iew(controller: controller),
            ChangeLanguage(controller: controller),
            MenuCards(),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 40.0),
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: StyleRepo.red,
                ),
                onPressed: controller.logout,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Assets.icons.logOut.svg(
                      colorFilter:
                          ColorFilter.mode(StyleRepo.white, BlendMode.srcIn),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      tr(LocaleKeys.Log_Out),
                      style: Theme.of(context)
                          .textTheme
                          .titleMedium!
                          .copyWith(color: StyleRepo.white),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}
