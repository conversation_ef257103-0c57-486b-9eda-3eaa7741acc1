import 'package:get/get.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:shop_app/core/config/app_builder.dart';
import 'package:shop_app/core/routes/routes.dart';
import 'package:shop_app/core/services/rest_api/rest_api.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shop_app/features/MyOrders/controller.dart';

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

/// يجب تسجيلها في main.dart أيضًا
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  showLocalNotification(message);
}

void showLocalNotification(RemoteMessage message) async {
  RemoteNotification? notification = message.notification;

  if (notification != null) {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'your_channel_id',
      'الإشعارات',
      importance: Importance.max,
      priority: Priority.high,
    );
    const NotificationDetails platformChannelSpecifics =
        NotificationDetails(android: androidPlatformChannelSpecifics);

    // أرسل payload (مثلا رقم الطلب أو أي قيمة)
    await flutterLocalNotificationsPlugin.show(
      notification.hashCode,
      notification.title,
      notification.body,
      platformChannelSpecifics,
      payload: 'order_payload_example', // هنا يمكنك إرسال بيانات واقعية
    );
  }
}

class NotificationController extends GetxService {
  final message = ''.obs;
  final fcmToken = ''.obs;
  final appBuilder = Get.find<AppBuilder>();

  @override
  void onInit() {
    super.onInit();
    _initFirebaseMessaging();
  }

  /// تهيئة Firebase Messaging و local notification
  void _initFirebaseMessaging() async {
    await Firebase.initializeApp();

    // تهيئة الإشعارات المحلية
    const androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    final initSettings = InitializationSettings(android: androidSettings);

    await flutterLocalNotificationsPlugin.initialize(
      initSettings,
      onDidReceiveNotificationResponse: (NotificationResponse response) {
        final payload = response.payload;
        if (payload != null) {
          // التنقل عند الضغط على الإشعار
          Get.toNamed(Pages.MyOrders.value, arguments: appBuilder.IsDelivery);
        }
      },
    );

    await FirebaseMessaging.instance.requestPermission();
    sendTokenToBackendIfRequired();
    // استقبال الإشعار أثناء استخدام التطبيق
    FirebaseMessaging.onMessage.listen((RemoteMessage msg) {
      message.value = msg.notification?.body ?? 'وصل إشعار جديد';
      showLocalNotification(msg);
      if (Get.isRegistered<MyOrdersPageController>()) {
        final myOrdersPageController = Get.find<MyOrdersPageController>();
        myOrdersPageController.pagerCurrentController.refreshData();
        myOrdersPageController.pagerPreviousController.refreshData();
      }
    });

    // عند فتح التطبيق من الإشعار
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage msg) {
      message.value = msg.notification?.body ?? 'تم فتح الإشعار';
    });

    // استقبال الإشعارات بالخلفية
    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
  }

  /// إرسال التوكن للسيرفر في حال لزم الأمر
  sendTokenToBackendIfRequired() async {
    final newToken = await FirebaseMessaging.instance.getToken();

    if (newToken != null) {
      ResponseModel response = await APIService.instance.request(
        Request(
          endPoint: appBuilder.IsDelivery
              ? EndPoints.Fcm_tokendelivery
              : EndPoints.Fcm_token,
          method: RequestMethod.Post,
          body: {"token": newToken},
        ),
      );
      if (response.success) {
        fcmToken.value = newToken;
      }
    }
  }
}
