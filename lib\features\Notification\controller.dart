import 'package:get/get.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:shop_app/core/config/app_builder.dart';
import 'package:shop_app/core/routes/routes.dart';
import 'package:shop_app/core/services/rest_api/rest_api.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

/// يجب تسجيلها في main.dart أيضًا
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  _showLocalNotification(message);
}

void _showLocalNotification(RemoteMessage message) async {
  RemoteNotification? notification = message.notification;

  if (notification != null) {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'your_channel_id',
      'الإشعارات',
      importance: Importance.max,
      priority: Priority.high,
    );
    const NotificationDetails platformChannelSpecifics =
        NotificationDetails(android: androidPlatformChannelSpecifics);

    // أرسل payload (مثلا رقم الطلب أو أي قيمة)
    await flutterLocalNotificationsPlugin.show(
      notification.hashCode,
      notification.title,
      notification.body,
      platformChannelSpecifics,
      payload: 'order_payload_example', // هنا يمكنك إرسال بيانات واقعية
    );
  }
}

class NotificationController extends GetxController {
  var message = ''.obs;
  var fcmToken = ''.obs;
  final controller = Get.find<AppBuilder>();
  @override
  void onInit() {
    super.onInit();
    initFirebaseMessaging();
  }

  void initFirebaseMessaging() async {
    await Firebase.initializeApp();

    // تهيئة flutter_local_notifications مع معالج الضغط على الإشعار
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    final InitializationSettings initializationSettings =
        InitializationSettings(android: initializationSettingsAndroid);

    await flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (NotificationResponse response) {
        final payload = response.payload;
        if (payload != null) {
          // عند الضغط على الإشعار ننتقل إلى صفحة الطلب مع تمرير البيانات
          Get.toNamed(Pages.MyOrders.value, arguments: controller.IsDelivery);
        }
      },
    );

    await FirebaseMessaging.instance.requestPermission();

    String? token = await FirebaseMessaging.instance.getToken();
    if (token != null) {
      fcmToken.value = token;
      sendTokenToBackend(token);
    }

    FirebaseMessaging.onMessage.listen((RemoteMessage messageData) {
      message.value = messageData.notification?.body ?? 'وصل إشعار جديد';
      _showLocalNotification(messageData);
    });

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage messageData) {
      message.value = messageData.notification?.body ?? 'تم فتح الإشعار';
      // يمكنك التعامل مع فتح التطبيق من إشعار هنا إذا أردت
    });

    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
  }

  sendTokenToBackend(String token) async {
    await APIService.instance.request(
      Request(
        endPoint: EndPoints.Fcm_token,
        method: RequestMethod.Post,
        body: {"token": token},
      ),
    );
  }
}
