import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/config/StatusOrder.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/features/MyOrders/Model/OderModel.dart';
import 'package:shop_app/features/MyOrders/controller.dart';
import 'package:shop_app/features/MyOrders/widgets/buildExpandedContent.dart';
import 'package:shop_app/features/MyOrders/widgets/buildProgressBar.dart';
import 'package:shop_app/features/MyOrders/widgets/buildStatusBadge.dart';

class OrderCard extends StatelessWidget {
  final OrderModel Order;

  final bool isExpanded; // حالة التوسيع تأتي من الخارج
  final VoidCallback onToggleExpansion; // لتبديل الحالة
  final Function(int orderNumber)? onMarkAsReceived;

  const OrderCard({
    super.key,
    required this.isExpanded,
    required this.onToggleExpansion,
    this.onMarkAsReceived,
    required this.Order,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<MyOrdersPageController>();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            StyleRepo.white,
            StyleRepo.lightMetaBlue.withValues(alpha: 0.3),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: StyleRepo.metaBlue.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: StyleRepo.metaBlue.withValues(alpha: 0.05),
            blurRadius: 40,
            offset: const Offset(0, 20),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(
          color: StyleRepo.metaBlue.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Main Card Content
          GestureDetector(
            onTap: () => controller.toggleExpansion(Order.id),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 5),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header Section
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Order Number
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [StyleRepo.metaBlue, StyleRepo.metaSkyBlue],
                          ),
                          borderRadius: BorderRadius.circular(25),
                          boxShadow: [
                            BoxShadow(
                              color: StyleRepo.metaBlue.withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Text(
                          '#' + Order.id.toString(),
                          style: const TextStyle(
                            color: StyleRepo.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                      ),
                      // Status Badge and Expand Icon
                      // داخل Row (Status Badge and Expand Icon)
                      Row(
                        children: [
                          buildStatusBadge(
                            status: OrderStatusParser.fromString(Order.status),
                          ),
                          const SizedBox(width: 8),
                          AnimatedRotation(
                            turns: isExpanded ? 0.5 : 0,
                            duration: const Duration(milliseconds: 300),
                            child: Icon(
                              Icons.keyboard_arrow_down,
                              color: StyleRepo.metaBlue,
                              size: 24,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),
                  // Order Date
                  Row(
                    children: [
                      Icon(
                        Icons.access_time_rounded,
                        size: 16,
                        color: StyleRepo.darkGrey,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        DateFormat('dd/MM/yyyy - HH:mm')
                            .format(Order.createdAt),
                        maxLines: 1,
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                              overflow: TextOverflow.ellipsis,
                            ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 10),

                  // Progress Bar Section
                  buildProgressBar(order: Order),
                ],
              ),
            ),
          ),

          // Expandable Details Section
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            height: isExpanded ? null : 0,
            child: isExpanded
                ? buildExpandedContent(
                    status: OrderStatusParser.fromString(Order.status),
                    productsOrder: Order.items,
                    TotalPrice: Order.totalPrice,
                    onMarkAsReceived: onMarkAsReceived,
                    orderNumber: Order.id)
                : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }
}
