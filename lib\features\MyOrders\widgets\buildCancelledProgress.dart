import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:shop_app/core/config/StatusOrder.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/features/MyOrders/widgets/buildProgressConnector.dart';
import 'package:shop_app/features/MyOrders/widgets/buildProgressStep.dart';

class buildCancelledProgress extends StatelessWidget {
  final OrderStatus status;
  const buildCancelledProgress({super.key, required this.status});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        buildProgressStep(
            status: status,
            label: tr(LocaleKeys.pending),
            stepStatus: OrderStatus.pending,
            index: 0,
            totalSteps: 2),
        buildProgressConnector(
          index: 0,
          isActive: false,
          status: status,
        ),
        buildProgressStep(
            status: status,
            label: tr(LocaleKeys.cancelled),
            stepStatus: OrderStatus.cancelled,
            index: 1,
            totalSteps: 2),
      ],
    );
  }
}
