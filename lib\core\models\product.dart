import 'dart:convert';

class ProductsModel {
  int id;
  String name;
  String? description;
  double price;
  int quantity;

  List<Image>? images;

  ProductsModel({
    required this.id,
    required this.name,
    this.description,
    required this.price,
    required this.quantity,
    this.images,
  });

  factory ProductsModel.fromRawJson(String str) =>
      ProductsModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ProductsModel.fromJson(Map<String, dynamic> json) => ProductsModel(
        id: json["id"],
        name: json["name"],
        description: json["description"],
        price: (json["price"]).toDouble(),
        quantity: json["quantity"],
        images: List<Image>.from(json["images"].map((x) => Image.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "description": description,
        "price": price,
        "quantity": quantity,
        "images": List<dynamic>.from(images!.map((x) => x.toJson())),
      };
}

class Image {
  String image;

  Image({
    required this.image,
  });

  factory Image.fromJson(Map<String, dynamic> json) => Image(
        image: json["image"],
      );

  Map<String, dynamic> toJson() => {
        "image": image,
      };
}
