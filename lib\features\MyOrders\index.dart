import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/config/app_builder.dart';
import 'package:shop_app/core/constants/controllers_tags.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/services/pagination/options/list_view.dart';
import 'package:shop_app/core/services/state_management/widgets/obs_widget.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/features/MyOrders/Model/OderModel.dart';
import 'package:shop_app/features/MyOrders/controller.dart';
import 'package:shop_app/features/MyOrders/widgets/MyOrdersCard.dart';
import 'package:shop_app/features/products/categories/widgets/NoResultsFound.dart';

class MyOrdersPage extends StatelessWidget {
  MyOrdersPage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(MyOrdersPageController());
    final appBuilder = Get.find<AppBuilder>();
    return DefaultTabController(
      length: 2,
      child: Scaffold(
          appBar: AppBar(
            automaticallyImplyLeading: false,
            centerTitle: true,
            title: Text(tr(LocaleKeys.My_Orders)),
            actions: [
              if (controller.isDelivery)
                Row(
                  children: [
                    ElevatedButton.icon(
                      onPressed: () {
                        appBuilder.logout();
                      },
                      label: const Icon(Icons.logout, color: Colors.white),
                      style: ElevatedButton.styleFrom(
                        fixedSize: Size(5, 1),
                        backgroundColor: StyleRepo.red,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        shadowColor: Colors.black.withValues(alpha: 0.3),
                      ),
                    ),
                    SizedBox(
                      width: 10,
                    )
                  ],
                )
            ],
            bottom: TabBar(
              tabs: [
                Tab(
                    icon: Icon(Icons.pending_actions),
                    text: tr(LocaleKeys.Current_Orders)),
                Tab(
                    icon: Icon(Icons.assignment_turned_in),
                    text: tr(LocaleKeys.Previous_Orders)),
              ],
            ),
          ),
          body: TabBarView(
            children: [
              RefreshIndicator(
                onRefresh: () async {
                  await controller.refreshCurrentData();
                },
                child: ListViewPagination.separated(
                  //
                  tag: ControllersTags.OrdersCurrent_pager,
                  fetchApi: controller.fetchCurrentData,
                  fromJson: OrderModel.fromJson,

                  //
                  initialLoading:
                      Center(child: CircularProgressIndicator.adaptive()),
                  errorWidget: (error) => Text(error),
                  onControllerInit: (pagerController) =>
                      controller.pagerCurrentController = pagerController,
                  separatorBuilder: (_, __) => SizedBox(height: 16),
                  itemBuilder: (context, index, product) {
                    return OrderCard(
                      Order: product,
                      isExpanded:
                          controller.expandedStates[product.id] ?? false,
                      onToggleExpansion: () =>
                          controller.toggleExpansion(product.id),
                      onMarkAsReceived: controller.comleteOrders,
                    );
                  },
                ),
              ),
              RefreshIndicator(
                onRefresh: () async {
                  await controller.refreshPreviousData();
                },
                child: ListViewPagination.separated(
                  //
                  tag: ControllersTags.OrdersPrevious_pager,
                  fetchApi: controller.fetchPreviousData,
                  fromJson: OrderModel.fromJson,
                  //
                  initialLoading:
                      Center(child: CircularProgressIndicator.adaptive()),
                  errorWidget: (error) => Text(error),
                  onControllerInit: (pagerController) =>
                      controller.pagerPreviousController = pagerController,
                  separatorBuilder: (_, __) => SizedBox(height: 16),
                  itemBuilder: (context, index, product) {
                    return OrderCard(
                      Order: product,
                      isExpanded:
                          controller.expandedStates[product.id] ?? false,
                      onToggleExpansion: () =>
                          controller.toggleExpansion(product.id),
                      onMarkAsReceived: controller.comleteOrders,
                    );
                  },
                ),
              ),
            ],
          )

          // ObsListBuilder(
          //   obs: controller.Myorder,
          //   errorBuilder: (context, error) {
          //     return NoResultsFound(Errore: error);
          //   },
          //   onRefresh: () async {
          //     await controller.fetchMyOrders();
          //   },
          //   builder: (context, OffersProduct) {
          //     return TabBarView(
          //       children: [
          //         // 🟣 تبويب الطلبات النشطة فقط
          //         RefreshIndicator(
          //           onRefresh: () => controller.fetchMyOrders(),
          //           child: SingleChildScrollView(
          //             physics: const AlwaysScrollableScrollPhysics(),
          //             padding: EdgeInsets.symmetric(vertical: 15),
          //             child: Column(
          //               children: List.generate(
          //                 controller.filteredCurrentOrders(OffersProduct).length,
          //                 (index) => OrderCard(
          //                   Order: controller
          //                       .filteredCurrentOrders(OffersProduct)[index],
          //                   isExpanded: controller.expandedStates[controller
          //                           .filteredCurrentOrders(OffersProduct)[index]
          //                           .id] ??
          //                       false,
          //                   onToggleExpansion: () => controller.toggleExpansion(
          //                       controller
          //                           .filteredCurrentOrders(OffersProduct)[index]
          //                           .id),
          //                   onMarkAsReceived: controller.comleteOrders,
          //                 ),
          //               ),
          //             ),
          //           ),
          //         ),
          //         // 🔵 تبويب الطلبات المكتملة/الملغية
          //         RefreshIndicator(
          //           onRefresh: () => controller.fetchMyOrders(),
          //           child: SingleChildScrollView(
          //             physics: const AlwaysScrollableScrollPhysics(),
          //             padding: EdgeInsets.symmetric(vertical: 15),
          //             child: Column(
          //               children: List.generate(
          //                   controller
          //                       .filteredPreviousOrders(OffersProduct)
          //                       .length,
          //                   (index) => OrderCard(
          //                         Order: controller.filteredPreviousOrders(
          //                             OffersProduct)[index],
          //                         isExpanded: controller.expandedStates[controller
          //                                 .filteredPreviousOrders(
          //                                     OffersProduct)[index]
          //                                 .id] ??
          //                             false,
          //                         onToggleExpansion: () =>
          //                             controller.toggleExpansion(controller
          //                                 .filteredPreviousOrders(
          //                                     OffersProduct)[index]
          //                                 .id),
          //                         onMarkAsReceived: controller
          //                             .comleteOrders, // تمرير الدالة مباشرة
          //                       )),
          //             ),
          //           ),
          //         )
          //       ],
          //     );
          //   },
          // ),
          ),
    );
  }
}





/*import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/config/app_builder.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/services/state_management/widgets/obs_widget.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/features/MyOrders/controller.dart';
import 'package:shop_app/features/MyOrders/widgets/MyOrdersCard.dart';
import 'package:shop_app/features/products/categories/widgets/NoResultsFound.dart';

class MyOrdersPage extends StatelessWidget {
  MyOrdersPage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(MyOrdersPageController());
    final appBuilder = Get.find<AppBuilder>();
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          centerTitle: true,
          title: Text(tr(LocaleKeys.My_Orders)),
          actions: [
            if (controller.isDelivery)
              Row(
                children: [
                  ElevatedButton.icon(
                    onPressed: () {
                      appBuilder.logout();
                    },
                    label: const Icon(Icons.logout, color: Colors.white),
                    style: ElevatedButton.styleFrom(
                      fixedSize: Size(5, 1),
                      backgroundColor: StyleRepo.red,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      shadowColor: Colors.black.withValues(alpha: 0.3),
                    ),
                  ),
                  SizedBox(
                    width: 10,
                  )
                ],
              )
          ],
          bottom: TabBar(
            tabs: [
              Tab(
                  icon: Icon(Icons.pending_actions),
                  text: tr(LocaleKeys.Current_Orders)),
              Tab(
                  icon: Icon(Icons.assignment_turned_in),
                  text: tr(LocaleKeys.Previous_Orders)),
            ],
          ),
        ),
        body: ObsListBuilder(
          obs: controller.Myorder,
          errorBuilder: (context, error) {
            return NoResultsFound(Errore: error);
          },
          onRefresh: () async {
            await controller.fetchMyOrders();
          },
          builder: (context, OffersProduct) {
            return TabBarView(
              children: [
                // 🟣 تبويب الطلبات النشطة فقط
                RefreshIndicator(
                  onRefresh: () => controller.fetchMyOrders(),
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    padding: EdgeInsets.symmetric(vertical: 15),
                    child: Column(
                      children: List.generate(
                        controller.filteredCurrentOrders(OffersProduct).length,
                        (index) => OrderCard(
                          Order: controller
                              .filteredCurrentOrders(OffersProduct)[index],
                          isExpanded: controller.expandedStates[controller
                                  .filteredCurrentOrders(OffersProduct)[index]
                                  .id] ??
                              false,
                          onToggleExpansion: () => controller.toggleExpansion(
                              controller
                                  .filteredCurrentOrders(OffersProduct)[index]
                                  .id),
                          onMarkAsReceived: controller.comleteOrders,
                        ),
                      ),
                    ),
                  ),
                ),
                // 🔵 تبويب الطلبات المكتملة/الملغية
                RefreshIndicator(
                  onRefresh: () => controller.fetchMyOrders(),
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    padding: EdgeInsets.symmetric(vertical: 15),
                    child: Column(
                      children: List.generate(
                          controller
                              .filteredPreviousOrders(OffersProduct)
                              .length,
                          (index) => OrderCard(
                                Order: controller.filteredPreviousOrders(
                                    OffersProduct)[index],
                                isExpanded: controller.expandedStates[controller
                                        .filteredPreviousOrders(
                                            OffersProduct)[index]
                                        .id] ??
                                    false,
                                onToggleExpansion: () =>
                                    controller.toggleExpansion(controller
                                        .filteredPreviousOrders(
                                            OffersProduct)[index]
                                        .id),
                                onMarkAsReceived: controller
                                    .comleteOrders, // تمرير الدالة مباشرة
                              )),
                    ),
                  ),
                )
              ],
            );
          },
        ),
      ),
    );
  }
}
*/