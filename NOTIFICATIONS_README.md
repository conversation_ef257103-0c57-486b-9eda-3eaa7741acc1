# نظام الإشعارات Firebase - دليل الاستخدام

## نظرة عامة
تم إنشاء نظام إشعارات متكامل باستخدام Firebase Cloud Messaging (FCM) لإدارة إشعارات حالة الطلبات والعروض والإشعارات العامة.

## الميزات المتوفرة

### 1. أنواع الإشعارات
- **إشعارات الطلبات**: تحديثات حالة الطلب (قيد المراجعة، قيد التحضير، في الطريق، تم التسليم)
- **إشعارات العروض**: عروض خاصة وخصومات
- **إشعارات عامة**: رسائل ترحيبية ومعلومات عامة

### 2. إدارة الإشعارات
- عرض جميع الإشعارات في صفحة مخصصة
- تمييز الإشعارات غير المقروءة
- عداد الإشعارات غير المقروءة
- إمكانية حذف الإشعارات
- تحديد الإشعارات كمقروءة
- مسح جميع الإشعارات

### 3. التنقل الذكي
- التنقل التلقائي حسب نوع الإشعار
- فتح تفاصيل الطلب عند النقر على إشعار الطلب
- التنقل إلى الصفحة الرئيسية للعروض

## الملفات المضافة

### خدمات Firebase
- `lib/core/services/firebase/notification_service.dart` - خدمة إدارة الإشعارات
- `lib/core/services/firebase/order_status_service.dart` - خدمة إدارة حالة الطلبات

### صفحة الإشعارات
- `lib/features/notifications/index.dart` - الصفحة الرئيسية للإشعارات
- `lib/features/notifications/controller.dart` - كونترولر صفحة الإشعارات

### ويدجتس الإشعارات
- `lib/features/notifications/widgets/notification_card.dart` - كارت الإشعار
- `lib/features/notifications/widgets/notification_icon.dart` - أيقونة الإشعارات مع العداد
- `lib/features/notifications/widgets/test_notifications.dart` - أدوات اختبار الإشعارات

### إعدادات Firebase
- `lib/firebase_options.dart` - خيارات تكوين Firebase
- تحديث `lib/main.dart` لتهيئة الخدمات

## كيفية الاستخدام

### 1. الوصول إلى صفحة الإشعارات
```dart
// من أي مكان في التطبيق
Get.toNamed('/notifications');

// أو من خلال كارت الإشعارات في صفحة الحساب
```

### 2. إضافة إشعار جديد برمجياً
```dart
final notificationService = NotificationService.instance;

final notification = NotificationModel(
  id: 'unique_id',
  title: 'عنوان الإشعار',
  body: 'نص الإشعار',
  data: {'key': 'value'},
  timestamp: DateTime.now(),
  type: NotificationType.general,
);

notificationService.addNotification(notification);
```

### 3. تتبع حالة الطلبات
```dart
final orderStatusService = OrderStatusService.instance;

// إضافة طلب للمراقبة
orderStatusService.trackOrder(orderModel);

// تحديث حالة الطلب (سيرسل إشعار تلقائياً)
orderStatusService.updateOrderStatus(orderId, OrderStatus.processing);
```

### 4. استخدام أيقونة الإشعارات
```dart
// في AppBar
AppBarNotificationIcon()

// في BottomNavigationBar
BottomNavNotificationIcon(isSelected: true)

// أيقونة مخصصة
NotificationIcon(
  iconColor: Colors.blue,
  iconSize: 24,
  onTap: () => Get.toNamed('/notifications'),
)
```

## اختبار النظام

### استخدام أدوات الاختبار المدمجة
في صفحة الإشعارات، ستجد ويدجت اختبار يحتوي على:

1. **إنشاء طلب تجريبي** - ينشئ طلب وهمي للاختبار
2. **محاكاة تحديث الطلب** - يحدث حالة الطلب تدريجياً
3. **إرسال إشعار عرض** - يرسل إشعار عرض تجريبي
4. **إرسال إشعار عام** - يرسل إشعار عام تجريبي

### اختبار الإشعارات يدوياً
```dart
// إرسال إشعار طلب
final orderNotification = NotificationModel(
  id: 'order_123',
  title: 'تحديث الطلب',
  body: 'طلبك رقم #123 قيد التحضير',
  data: {
    'type': 'order_status',
    'order_id': '123',
    'status': 'processing'
  },
  timestamp: DateTime.now(),
  type: NotificationType.orderUpdate,
);

NotificationService.instance.addNotification(orderNotification);
```

## إعداد Firebase (للإنتاج)

### 1. إنشاء مشروع Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. أنشئ مشروع جديد أو استخدم مشروع موجود
3. فعّل Firebase Cloud Messaging

### 2. إضافة التطبيق
1. أضف تطبيق Android/iOS إلى المشروع
2. حمّل ملف `google-services.json` (Android) أو `GoogleService-Info.plist` (iOS)
3. ضع الملف في المجلد المناسب

### 3. تحديث إعدادات Firebase
حدّث ملف `lib/firebase_options.dart` بالقيم الصحيحة من مشروعك:

```dart
static const FirebaseOptions android = FirebaseOptions(
  apiKey: 'your-api-key',
  appId: 'your-app-id',
  messagingSenderId: 'your-sender-id',
  projectId: 'your-project-id',
  storageBucket: 'your-storage-bucket',
);
```

## إرسال الإشعارات من الخادم

### استخدام Firebase Admin SDK
```javascript
const admin = require('firebase-admin');

const message = {
  notification: {
    title: 'تحديث الطلب',
    body: 'طلبك رقم #123 تم تسليمه بنجاح'
  },
  data: {
    type: 'order_status',
    order_id: '123',
    status: 'completed'
  },
  token: 'user-fcm-token'
};

admin.messaging().send(message);
```

### استخدام REST API
```bash
curl -X POST https://fcm.googleapis.com/fcm/send \
  -H "Authorization: key=YOUR_SERVER_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "USER_FCM_TOKEN",
    "notification": {
      "title": "تحديث الطلب",
      "body": "طلبك رقم #123 تم تسليمه بنجاح"
    },
    "data": {
      "type": "order_status",
      "order_id": "123",
      "status": "completed"
    }
  }'
```

## الأذونات المطلوبة

### Android
```xml
<!-- في android/app/src/main/AndroidManifest.xml -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
<uses-permission android:name="android.permission.VIBRATE" />
```

### iOS
```xml
<!-- في ios/Runner/Info.plist -->
<key>UIBackgroundModes</key>
<array>
    <string>remote-notification</string>
</array>
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **الإشعارات لا تظهر**
   - تأكد من تهيئة Firebase بشكل صحيح
   - تحقق من أذونات الإشعارات
   - تأكد من صحة FCM Token

2. **الإشعارات لا تعمل في الخلفية**
   - تأكد من تسجيل `firebaseMessagingBackgroundHandler`
   - تحقق من إعدادات Background Modes في iOS

3. **مشاكل في التنقل**
   - تأكد من تسجيل جميع المسارات في `routes.dart`
   - تحقق من صحة البيانات المرسلة مع الإشعار

## الخطوات التالية

1. **ربط النظام بالخادم** - ربط تحديثات حالة الطلبات بالخادم الفعلي
2. **إضافة إشعارات محلية** - للتذكيرات والمواعيد
3. **تحسين التصميم** - إضافة رسوم متحركة وتأثيرات بصرية
4. **إضافة فلترة الإشعارات** - حسب النوع والتاريخ
5. **إعدادات الإشعارات** - السماح للمستخدم بتخصيص أنواع الإشعارات

## الدعم
للمساعدة أو الاستفسارات، يرجى مراجعة:
- [Firebase Documentation](https://firebase.google.com/docs/cloud-messaging)
- [Flutter Firebase Messaging](https://firebase.flutter.dev/docs/messaging/overview/)
