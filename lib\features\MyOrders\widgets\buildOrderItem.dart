import 'package:flutter/material.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/style/repo.dart';

class buildOrderItem extends StatelessWidget {
  const buildOrderItem({
    super.key,
    required this.name,
    required this.quantity,
    required this.price,
  });

  final String name;
  final int quantity;
  final double price;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          // Item Icon
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: StyleRepo.metaBlue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.fastfood,
              color: StyleRepo.metaBlue,
              size: 20,
            ),
          ),

          const SizedBox(width: 12),

          // Item Details
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                name,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: StyleRepo.black,
                ),
              ),
              Text(
                '${LocaleKeys.Quantity} : $quantity',
                style: TextStyle(
                  fontSize: 12,
                  color: StyleRepo.darkGrey,
                ),
              ),
              Text(
                '${LocaleKeys.Price} : $price',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: StyleRepo.purple,
                ),
              ),
            ],
          ),

          Spacer(),
          Text(
            '${quantity * price}  \$',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: StyleRepo.metaBlue,
            ),
          ),
        ],
      ),
    );
  }
}
