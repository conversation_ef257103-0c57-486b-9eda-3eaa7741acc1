import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/style/repo.dart';

enum OrderStatus {
  pending,
  processing,
  outForDelivery,
  completed,
  cancelled,
  rejected,
}

// ✅ الامتداد الأول لتحويل String إلى Enum والعكس
extension OrderStatusParser on OrderStatus {
  static OrderStatus fromString(String value) {
    switch (value.toLowerCase()) {
      case LocaleKeys.pending:
        return OrderStatus.pending;
      case LocaleKeys.processing:
        return OrderStatus.processing;
      case LocaleKeys.out_for_delivery:
        return OrderStatus.outForDelivery;
      case LocaleKeys.completed:
        return OrderStatus.completed;
      case LocaleKeys.cancelled:
        return OrderStatus.cancelled;
      case LocaleKeys.rejected:
        return OrderStatus.rejected;
      default:
        throw Exception('${LocaleKeys.Unknown_OrderStatus}  $value');
    }
  }

  String toApiString() {
    switch (this) {
      case OrderStatus.pending:
        return LocaleKeys.pending;
      case OrderStatus.processing:
        return LocaleKeys.processing;
      case OrderStatus.outForDelivery:
        return LocaleKeys.out_for_delivery;
      case OrderStatus.completed:
        return LocaleKeys.completed;
      case OrderStatus.cancelled:
        return LocaleKeys.cancelled;
      case OrderStatus.rejected:
        return LocaleKeys.rejected;
    }
  }
}

// ✅ الامتداد الثاني لعرض الحالة والأيقونة واللون
extension OrderStatusUI on OrderStatus {
  String get label {
    switch (this) {
      case OrderStatus.pending:
        return tr(LocaleKeys.pending);
      case OrderStatus.processing:
        return tr(LocaleKeys.processing);
      case OrderStatus.completed:
        return tr(LocaleKeys.completed);
      case OrderStatus.cancelled:
        return tr(LocaleKeys.cancelled);
      case OrderStatus.rejected:
        return tr(LocaleKeys.rejected);
      case OrderStatus.outForDelivery:
        return tr(LocaleKeys.out_for_delivery);
    }
  }

  Color get color {
    switch (this) {
      case OrderStatus.pending:
        return StyleRepo.indigo;
      case OrderStatus.processing:
        return StyleRepo.orange;
      case OrderStatus.completed:
        return StyleRepo.green;
      case OrderStatus.cancelled:
        return StyleRepo.red;
      case OrderStatus.rejected:
        return StyleRepo.red;
      case OrderStatus.outForDelivery:
        return StyleRepo.indigo;
    }
  }

  IconData get icon {
    switch (this) {
      case OrderStatus.pending:
        return Icons.schedule_rounded;
      case OrderStatus.processing:
        return Icons.precision_manufacturing_rounded;
      case OrderStatus.outForDelivery:
        return Icons.local_shipping_rounded;
      case OrderStatus.completed:
        return Icons.check_circle_rounded;
      case OrderStatus.cancelled:
        return Icons.cancel_rounded;
      case OrderStatus.rejected:
        return Icons.block_rounded;
    }
  }
}
