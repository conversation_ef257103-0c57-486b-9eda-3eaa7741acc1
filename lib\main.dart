import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shop_app/core/routes/routes.dart';
import 'package:shop_app/core/services/firebase/notification_service.dart';
import 'package:shop_app/core/services/firebase/order_status_service.dart';
import 'package:shop_app/core/style/style.dart';
import 'package:shop_app/firebase_options.dart';
import 'core/config/app_builder.dart';
import 'core/localization/localization.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();

  // تهيئة GetStorage
  await GetStorage.init();

  // تهيئة Firebase
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
  } catch (e) {
    // Firebase already initialized
    print('Firebase already initialized: $e');
  }

  // تعيين معالج الرسائل في الخلفية
  FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

  // تهيئة الخدمات
  await _initializeServices();

  runApp(
    EasyLocalization(
      supportedLocales: AppLocalization.values.map((e) => e.locale).toList(),
      path: "assets/translations",
      // assetLoader: const CodegenLoader(),
      fallbackLocale: AppLocalization.en.locale,
      child: const MyApp(),
    ),
  );
}

// تهيئة الخدمات
Future<void> _initializeServices() async {
  // تهيئة خدمة الإشعارات
  Get.put(NotificationService(), permanent: true);

  // تهيئة خدمة حالة الطلبات
  Get.put(OrderStatusService(), permanent: true);
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    Get.put(AppBuilder());
    return GetMaterialApp(
      title: 'Flutter Demo',
      theme: AppStyle.theme,
      localizationsDelegates: context.localizationDelegates,
      locale: context.locale,
      supportedLocales: context.supportedLocales,
      //
      initialRoute: '/',
      unknownRoute: AppRouting.unknownRoute,
      getPages: AppRouting.routes,
    );
  }
}
