import 'package:flutter/material.dart';

class EmptyWidget extends StatelessWidget {
  final Future Function() refresh;
  const EmptyWidget({super.key, required this.refresh});

  @override
  Widget build(BuildContext context) {
    //TODO - pagination
    return RefreshIndicator(
      onRefresh: () => refresh(),
      child: <PERSON><PERSON>iew(
        physics:
            AlwaysScrollableScrollPhysics(), // مهمة لتمكين السحب حتى بدون عناصر
        children: [
          SizedBox(height: 300), // لإتاحة المساحة للسحب
          Center(child: Text("No data")),
        ],
      ),
    );
  }
}

class SliverEmptyWidget extends StatelessWidget {
  final Future Function() refresh;
  SliverEmptyWidget({super.key, required this.refresh});

  @override
  Widget build(BuildContext context) {
    return SliverList(
        delegate: SliverChildListDelegate([
      EmptyWidget(
        refresh: refresh,
      )
    ]));
  }
}
