import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/features/EditAccount/controller.dart';
import 'package:shop_app/features/EditAccount/widgets/PickImage.dart';
import 'package:shop_app/features/EditAccount/widgets/TextFormEditProfile.dart';
import 'package:shop_app/gen/assets.gen.dart';

class EditAccountPage extends StatelessWidget {
  const EditAccountPage({super.key});
  @override
  Widget build(BuildContext context) {
    final controller = Get.put(EditAccountPageController());

    return Scaffold(
      appBar: AppBar(
        toolbarHeight: MediaQuery.of(context).size.height * .05,
        automaticallyImplyLeading: false,
        leading: IconButton(
          onPressed: () => Get.back(),
          icon: Assets.icons.back.svg(color: StyleRepo.black),
          iconSize: 24,
        ),
        title: Center(
          child: Text(tr(LocaleKeys.Edit_profile),
              style: Theme.of(context).textTheme.titleMedium),
        ),
      ),
      body: Form(
        key: controller.formKey,
        child: ListView(
          padding: EdgeInsets.symmetric(horizontal: 16),
          children: [
            Center(child: PickImageMyProfile()),
            SizedBox(
              height: 12,
            ),
            Text(tr(LocaleKeys.User_name),
                style: Theme.of(context).textTheme.bodyLarge),
            const SizedBox(height: 6),
            TextFormEditProfile(
              controller: controller.name,
              icon: Assets.icons.profile.svg(),
              hintText: LocaleKeys.Name,
              validator: (value) {
                if (value!.isEmpty) {
                  return tr(LocaleKeys.This_field_is_required);
                }
                return null;
              },
            ),
            const SizedBox(height: 15),
            Text(tr(LocaleKeys.Barcode),
                style: Theme.of(context).textTheme.bodyLarge),
            const SizedBox(height: 6),
            TextFormEditProfile(
              controller: controller.barcode,
              icon: Icon(Icons.qr_code),
              hintText: LocaleKeys.Barcode_Ex,
              validator: (value) {
                if (value!.isEmpty) {
                  return tr(LocaleKeys.This_field_is_required);
                }

                return null;
              },
            ),
            const SizedBox(height: 15),
            Text(tr(LocaleKeys.Phone_number),
                style: Theme.of(context).textTheme.bodyLarge),
            const SizedBox(height: 6),
            TextFormEditProfile(
              controller: controller.phoneNumber,
              icon: Icon(Icons.phone),
              input: TextInputType.phone,
              hintText: LocaleKeys.hintNum,
              validator: (value) {
                if (value!.isEmpty) {
                  return tr(LocaleKeys.This_field_is_required);
                }
                if (!RegExp(r'^[0-9]+$').hasMatch(value))
                  return tr(LocaleKeys.only_numbers_allowed);

                return null;
              },
            ),
            const SizedBox(height: 15),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              child: ElevatedButton(
                onPressed: controller.confirm,
                child: Text(
                  tr(LocaleKeys.save),
                  style: TextStyle(color: StyleRepo.white, fontSize: 16),
                ),
              ),
            ),
            const SizedBox(
              height: 20,
            )
          ],
        ),
      ),
    );
  }
}
