import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/constants/controllers_tags.dart';
import 'package:shop_app/core/models/product.dart';
import 'package:shop_app/core/services/pagination/options/grid_view.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/core/widgets/product_card.dart';
import 'package:shop_app/features/products/PageProducts/controller.dart';
import 'package:shop_app/features/products/PageProducts/widgets/FilterShee.dart';
import 'package:shop_app/gen/assets.gen.dart';

class ProductsPage extends StatelessWidget {
  const ProductsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(ProductsPageController());
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        leading: IconButton(
            onPressed: Get.back,
            icon: Assets.icons.back.svg(color: StyleRepo.black)),
        toolbarHeight: MediaQuery.of(context).size.width * .2,
        flexibleSpace: Padding(
          padding: const EdgeInsets.only(bottom: 10, top: 40.0),
          child: Center(
            child: Row(
              children: [
                SizedBox(width: 60),
                SizedBox(
                  width: MediaQuery.of(context).size.width * .7,
                  height: 50,
                  child: TextField(
                    controller: controller.searchTextController,
                    decoration: InputDecoration(
                      prefixIcon: Icon(Icons.search, color: StyleRepo.black),
                      suffixIcon: IconButton(
                        icon: Assets.icons.closeSearch.svg(),
                        onPressed: () {
                          controller.clearSearch();
                        },
                      ),
                      hintText: 'Search Store',
                    ),
                  ),
                ),
                SizedBox(width: 10),
                Expanded(
                  child: IconButton(
                    onPressed: () {
                      controller.resetFilter();
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        builder: (context) => FilterSheet(),
                      );
                    },
                    icon: Assets.icons.fillter.svg(),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
      body: Container(
        padding: EdgeInsets.all(15),
        child: Expanded(
          child: GridViewPagination.builder(
            //
            tag: ControllersTags.products_pager,
            fetchApi: controller.fetchData,
            fromJson: ProductsModel.fromJson,
            //
            initialLoading: Center(child: CircularProgressIndicator.adaptive()),
            errorWidget: (error) => Text(error),
            onControllerInit: (pagerController) =>
                controller.pagerController = pagerController,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 0.63),

            itemBuilder: (context, index, product) {
              return ProductCard(product: product);
            },
          ),
        ),
      ),
    );
  }
}
