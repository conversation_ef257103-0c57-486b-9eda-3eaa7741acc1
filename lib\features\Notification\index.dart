import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/features/Notification/controller.dart';

class NotificationsPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final controller = Get.find<NotificationController>();

    return Scaffold(
      appBar: AppBar(title: Text('حالة الطلب مع GetX و FCM')),
      body: Center(
        child: Obx(() => Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('FCM Token:'),
                SelectableText(controller.fcmToken.value.isNotEmpty
                    ? controller.fcmToken.value
                    : 'جاري التحميل...'),
                SizedBox(height: 20),
                Text('رسالة الإشعار:'),
                SizedBox(height: 10),
                Text(controller.message.value.isNotEmpty
                    ? controller.message.value
                    : 'لا توجد إشعارات'),
              ],
            )),
      ),
    );
  }
}
