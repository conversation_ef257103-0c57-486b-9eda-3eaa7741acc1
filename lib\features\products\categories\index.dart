import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/features/home/<USER>';
import 'package:shop_app/features/products/categories/controller.dart';
import 'package:shop_app/features/products/categories/widgets/SearchResults.dart';
import 'package:shop_app/gen/assets.gen.dart';

class CategoriesPage extends StatelessWidget {
  const CategoriesPage({super.key});
  @override
  Widget build(BuildContext context) {
    final controller = Get.put(CategoriesPageController());
    return Column(
      children: [
        SizedBox(height: 10 + MediaQuery.viewPaddingOf(context).top),
        Text(
          tr(LocaleKeys.Find_Products),
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        SizedBox(
          height: 5,
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20),
          child: Text<PERSON>ield(
            controller: controller.searchController,
            onChanged: (value) {
              controller.searchProducts(value);
            },
            decoration: InputDecoration(
              hintText: tr(LocaleKeys.Search_Store),
              prefixIcon: Icon(Icons.search),
              suffixIcon: Obx(() => controller.searchQuery.value.isNotEmpty
                  ? IconButton(
                      icon: Assets.icons.closeSearch.svg(),
                      onPressed: () {
                        // مسح البحث في المتحكم
                        controller.clearSearch();
                      },
                    )
                  : SizedBox()),
            ),
          ),
        ),
        SizedBox(
          height: 10,
        ),

        // استخدام Obx لمراقبة حالة البحث
        Obx(
          () {
            // إذا كان المستخدم يبحث وهناك نتائج بحث
            if (controller.isSearching.value) {
              return SearchResults(
                choose: controller.searchResults,
                IsSearch: true,
              );
            } else {
              return SearchResults(
                choose: Get.find<HomePageController>().categories,
                IsSearch: false,
              );
            }
          },
        ),
        SizedBox(
          height: 10,
        )
      ],
    );
  }
}
