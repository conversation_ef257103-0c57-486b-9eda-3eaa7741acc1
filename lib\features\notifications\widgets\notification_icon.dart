import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/services/firebase/notification_service.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/gen/assets.gen.dart';

class NotificationIcon extends StatelessWidget {
  final VoidCallback? onTap;
  final Color? iconColor;
  final double? iconSize;

  const NotificationIcon({
    super.key,
    this.onTap,
    this.iconColor,
    this.iconSize = 24,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<NotificationService>(
      init: NotificationService.instance,
      builder: (notificationService) {
        return Obx(() {
          final unreadCount = notificationService.unreadCount.value;
          
          return InkWell(
            onTap: onTap ?? () => Get.toNamed('/notifications'),
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: const EdgeInsets.all(8),
              child: Stack(
                children: [
                  // أيقونة الإشعارات
                  Assets.icons.notification.svg(
                    colorFilter: ColorFilter.mode(
                      iconColor ?? StyleRepo.black,
                      BlendMode.srcIn,
                    ),
                    width: iconSize,
                    height: iconSize,
                  ),
                  
                  // عداد الإشعارات غير المقروءة
                  if (unreadCount > 0)
                    Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        min: 16,
                        height: 16,
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        decoration: BoxDecoration(
                          color: StyleRepo.red,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: StyleRepo.white,
                            width: 1,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            unreadCount > 99 ? '99+' : unreadCount.toString(),
                            style: TextStyle(
                              color: StyleRepo.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        });
      },
    );
  }
}

// ويدجت أيقونة الإشعارات للـ AppBar
class AppBarNotificationIcon extends StatelessWidget {
  const AppBarNotificationIcon({super.key});

  @override
  Widget build(BuildContext context) {
    return const NotificationIcon(
      iconColor: null, // سيستخدم اللون الافتراضي
      iconSize: 24,
    );
  }
}

// ويدجت أيقونة الإشعارات للـ BottomNavigationBar
class BottomNavNotificationIcon extends StatelessWidget {
  final bool isSelected;
  
  const BottomNavNotificationIcon({
    super.key,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return NotificationIcon(
      iconColor: isSelected ? StyleRepo.blue : StyleRepo.lightGrey,
      iconSize: 24,
    );
  }
}
