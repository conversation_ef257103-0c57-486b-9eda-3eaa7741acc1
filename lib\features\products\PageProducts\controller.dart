import 'package:dio/dio.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:shop_app/core/models/product.dart';
import 'package:shop_app/core/services/pagination/controller.dart';
import 'package:shop_app/core/services/rest_api/rest_api.dart';
import 'package:shop_app/core/services/state_management/obs.dart';
import 'package:shop_app/features/products/cart/controller.dart';

class ProductsPageController extends GetxController {
  var isCheckedList = <String, bool>{}.obs;
  final contoller = Get.find<CartPageController>();
  late int id;

  late PaginationController pagerController;

  Future<ResponseModel> fetchData(int page, CancelToken cancel) async {
    ResponseModel response = await APIService.instance.request(
      Request(
          endPoint: EndPoints.categorie(id),
          params: {"page": page},
          cancelToken: cancel,
          getdata: "products"),
    );

    return response;
  }

  refreshData() {
    pagerController.refreshData();
  }

  @override
  void onInit() {
    id = Get.arguments;
    super.onInit();
  }

  @override
  void onClose() {
    super.onClose();
  }

  ObsList<ProductsModel> products = ObsList([]);
  List<ProductsModel>? allProducts;
  // في تحديث المنتجات بعد الفلترة أو الاختيار:
  void updateSelectedProducts(List<String> FillterName) {
    if (products.value == null) return;
    if (allProducts == null) {
      allProducts = List.from(products.value!);
    }
    products.value = allProducts!
        .where((product) => FillterName.contains(product.name))
        .toList();

    products.refresh();
  }

  void resetFilter() {
    if (allProducts != null) {
      products.value = List.from(allProducts!); // ترجع النسخة الأصلية
      products.refresh();
      // تقدر كمان تمسح الاختيارات
      isCheckedList.value = {
        for (var p in products.value!) p.name: false,
      };
    }
  }

  TextEditingController searchTextController = TextEditingController();

  void clearSearch() {
    searchTextController.clear();
  }

  Future<bool> confirmExit(BuildContext context) async {
    bool? shouldLeave = await PanaraConfirmDialog.show(
      context,
      title: "تأكيد الخروج",
      message: "هل تريد تفريغ العربة والخروج؟",
      confirmButtonText: "نعم",
      cancelButtonText: "لا",
      panaraDialogType: PanaraDialogType.error, // يجب تحديد النوع هنا
      onTapCancel: () {
        Get.back(result: false);
      },
      onTapConfirm: () {
        contoller.clearCart(); // تأكد من كتابة اسم المتغير بشكل صحيح
        Get.back(result: true);
      },
    );

    return shouldLeave ?? false; // ترجع false إذا المستخدم ألغى
  }
}
