buildscript {
    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2' // حسب نسخة Gradle عندك
        classpath 'com.google.gms:google-services:4.3.15' // ✅ هذا مطلوب لـ Firebase
    }
}


allprojects {
    repositories {
    google()
    mavenCentral()
    // jcenter() // إذا موجود يمكن تجربته
}

}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
