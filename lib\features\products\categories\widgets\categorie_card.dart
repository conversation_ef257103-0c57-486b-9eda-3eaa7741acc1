import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/models/category.dart';
import 'package:shop_app/core/routes/routes.dart';
import 'package:shop_app/core/services/rest_api/constants/end_points.dart';
import 'package:shop_app/core/widgets/image.dart';

class CategorieCard extends StatelessWidget {
  final Color color_border;
  final Color color_fill;
  final CategoryModel Categories;
  CategorieCard({
    super.key,
    required this.color_border,
    required this.color_fill,
    required this.Categories,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Get.toNamed(Pages.PageProduct.value, arguments: Categories.id);
      },
      child: Container(
        width: MediaQuery.sizeOf(context).width * .4,
        decoration: BoxDecoration(
          color: color_fill,
          borderRadius: BorderRadius.circular(18),
          border: Border.all(
            color: color_border,
          ),
        ),
        child: Column(
          children: [
            SizedBox(
              height: 10,
            ),
            AppImage(
              height: 100,
              width: 125,
              decoration:
                  BoxDecoration(borderRadius: BorderRadius.circular(18)),
              path: Categories.image.isNotEmpty == true
                  ? EndPoints.baseUrl + Categories.image
                  : '',
              type: ImageType.CachedNetwork,
            ),
            SizedBox(
              height: 10,
            ),
            Expanded(
                child: Text(
              Categories.name,
              maxLines: 2,
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ))
          ],
        ),
      ),
    );
  }
}
