// ignore_for_file: constant_identifier_names

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/features/EditAccount/index.dart';
import 'package:shop_app/features/MyOrders/index.dart';
import 'package:shop_app/features/about_us/index.dart';
import 'package:shop_app/features/Location/Location%20Picker/index.dart';
import 'package:shop_app/features/Location/Location%20Details/index.dart';
import 'package:shop_app/features/main/index.dart';
import 'package:shop_app/features/notifications/index.dart';
import 'package:shop_app/features/products/cart/widgets/OrderComplete.dart';
import 'package:shop_app/features/products/PageProducts/index.dart';

import '../../features/auth/login/index.dart';
import '../../features/products/product_details/index.dart';
import '../../features/splash/index.dart';

class AppRouting {
  static GetPage unknownRoute =
      GetPage(name: "/unknown", page: () => SizedBox());

  static GetPage initialRoute = GetPage(
    name: "/",
    page: () => SplashScreen(),
  );

  static List<GetPage> routes = [
    initialRoute,
    ...Pages.values.map((e) => e.page),
  ];
}

enum Pages {
  //Auth
  login,
  //
  home,
  product_details,
  PageProduct,
  OrderComplete,
  editprofile,
  about_us,
  location_picker,
  location_details,
  MyOrders,
  notifications,
  //
  ;

  String get value => '/$name';

  GetPage get page => switch (this) {
        login => GetPage(
            name: value,
            page: () => LoginPage(),
          ),
        home => GetPage(
            name: value,
            page: () => MainPage(),
          ),
        product_details => GetPage(
            name: value,
            page: () => ProductDetailsPage(),
          ),
        PageProduct => GetPage(
            name: value,
            page: () => ProductsPage(),
          ),
        OrderComplete => GetPage(
            name: value,
            page: () => OrderCompletePage(),
          ),
        editprofile => GetPage(
            name: value,
            page: () => EditAccountPage(),
          ),
        about_us => GetPage(
            name: value,
            page: () => AboutUsPage(),
          ),
        location_picker => GetPage(
            name: value,
            page: () => LocationPickerPage(),
          ),
        location_details => GetPage(
            name: value,
            page: () => LocationDetailsPage(),
          ),
        MyOrders => GetPage(
            name: value,
            page: () => MyOrdersPage(),
          ),
        notifications => GetPage(
            name: value,
            page: () => NotificationsPage(),
          ),
      };
}
