import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/config/StatusOrder.dart';
import 'package:shop_app/features/MyOrders/Model/OderModel.dart';
import 'package:shop_app/features/MyOrders/controller.dart';
import 'package:shop_app/features/MyOrders/widgets/buildCancelledProgress.dart';
import 'package:shop_app/features/MyOrders/widgets/buildProgressConnector.dart';
import 'package:shop_app/features/MyOrders/widgets/buildProgressStep.dart';

class buildProgressSteps extends StatelessWidget {
  final OrderModel order;

  const buildProgressSteps({super.key, required this.order});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<MyOrdersPageController>();
    final OrderStatus status = OrderStatusParser.fromString(order.status);

    if (status == OrderStatus.cancelled || status == OrderStatus.rejected) {
      return buildCancelledProgress(status: status);
    }

    List<OrderStatus> steps = [
      OrderStatus.pending,
      OrderStatus.processing,
      OrderStatus.outForDelivery,
      OrderStatus.completed,
    ];
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int i = 0; i < 4; i++) ...[
          buildProgressStep(
            status: status,
            label: steps[i].label,
            stepStatus: steps[i],
            index: i,
            totalSteps: 4,
          ),
          if (i < 3)
            buildProgressConnector(
              index: i,
              isActive:
                  controller.isConnectorActive(status, i), // ✅ التصحيح هنا
              status: status,
            ),
        ],
      ],
    );
  }
}
