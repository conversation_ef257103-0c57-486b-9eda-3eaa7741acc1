import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shop_app/core/config/role.dart';
import 'package:shop_app/features/Notification/controller.dart';

import '../routes/routes.dart';
import '../services/rest_api/rest_api.dart';

class AppBuilder extends GetxService {
  GetStorage box = GetStorage("app");
  bool IsDelivery = false;
  late Role role;
  // GeneralUser? user;
  String? token;
  late NotificationController notificationController;

  loadData() async {
    await box.initStorage;

    if (!box.hasData("role")) {
      setRole(Role.new_user);
    } else {
      role = Role.fromString(box.read("role"));
    }
    if (!box.hasData("Delivery")) {
      setIsDelivery(IsDelivery);
    } else {
      IsDelivery = box.read("Delivery");
    }

    if (box.hasData("token")) {
      token = box.read("token");
    }
  }

  setRole(Role role) {
    this.role = role;
    box.write("role", role.name);
  }

  setIsDelivery(bool isDelivery) {
    this.IsDelivery = isDelivery;
    box.write("Delivery", isDelivery);
  }

  setToken(String? token) {
    this.token = token;
    Get.find<APIService>().setToken(token);
    if (token != null) {
      box.write("token", token);
      notificationController.sendTokenToBackendIfRequired();
    } else {
      box.remove("token");
    }
  }

  logout() {
    setRole(Role.unregistered);
    setToken(null);
    Get.find<APIService>().setToken(null);
    Get.offAllNamed(Pages.login.value);
  }

  init() async {
    await loadData();
    Get.put(APIService(token: token));
    notificationController =
        Get.put(NotificationController(), permanent: true); // أولاً
    if (role == Role.unregistered || role == Role.new_user) {
      Get.offNamed(Pages.login.value);
    } else if (IsDelivery) {
      Get.offNamed(Pages.MyOrders.value, arguments: true);
    } else {
      Get.offNamed(Pages.home.value, arguments: false);
    }
  }
}
