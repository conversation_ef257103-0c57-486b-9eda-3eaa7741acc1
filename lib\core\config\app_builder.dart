import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shop_app/core/config/role.dart';

import '../routes/routes.dart';
import '../services/rest_api/rest_api.dart';

class AppBuilder extends GetxService {
  GetStorage box = GetStorage("app");

  late Role role;
  // GeneralUser? user;
  String? token;

  loadData() async {
    await box.initStorage;

    if (!box.hasData("role")) {
      setRole(Role.new_user);
    } else {
      role = Role.fromString(box.read("role"));
    }

    if (box.hasData("token")) {
      token = box.read("token");
    }
  }

  setRole(Role role) {
    this.role = role;
    box.write("role", role.name);
  }

  // setUserData(GeneralUser user){}

  setToken(String? token) {
    this.token = token;
    Get.find<APIService>().setToken(token);
    if (token != null) {
      box.write("token", token);
    } else {
      box.remove("token");
    }
  }

  logout() {
    setRole(Role.unregistered);
    setToken(null);
    Get.find<APIService>().setToken(null);
    Get.offAllNamed(Pages.login.value);
  }

  init() async {
    await loadData();
    Get.put(APIService(token: token));
    if (role == Role.unregistered || role == Role.new_user) {
      Get.offNamed(Pages.login.value);
    } else if (role == Role.driver) {
      Get.offNamed(Pages.MyOrders.value, arguments: true);
    } else {
      Get.offNamed(Pages.home.value, arguments: false);
    }
  }
}
