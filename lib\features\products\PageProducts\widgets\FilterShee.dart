import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/services/state_management/widgets/obs_widget.dart';
import 'package:shop_app/features/products/PageProducts/controller.dart';

class FilterSheet extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ProductsPageController>();

    return SizedBox(
      height: MediaQuery.of(context).size.height * .8,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: ObsListBuilder(
            obs: controller.products,
            builder: (context, products) {
              return ListView(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      IconButton(
                          onPressed: () {
                            Get.back();
                          },
                          icon: Icon(Icons.close)),
                      Text(
                        'Filters',
                        style: TextStyle(
                            fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      SizedBox(width: 24), // للموازنة
                    ],
                  ),
                  SizedBox(height: 20),

                  // القسم الأول: Categories
                  Center(
                    child: Text(
                      'Categories',
                      style:
                          TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
                    ),
                  ),

                  ...List.generate(
                    controller.products.valueLength,
                    (index) {
                      return Obx(
                        () => CheckboxListTile(
                          title: Text(controller.products[index].name),
                          value: controller.isCheckedList[
                                  controller.products[index].name] ??
                              false,
                          onChanged: (val) {
                            controller.isCheckedList[
                                controller.products[index].name] = val!;
                          },
                        ),
                      );
                    },
                  ),

                  SizedBox(height: 24),

                  // زر التطبيق
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12)),
                    ),
                    onPressed: () {
                      controller.updateSelectedProducts(controller
                          .isCheckedList.entries
                          .where((e) => e.value)
                          .map((e) => e.key)
                          .toList());

                      Get.back(result: true);
                    },
                    child: Text('Apply Filter', style: TextStyle(fontSize: 16)),
                  ),
                ],
              );
            }),
      ),
    );
  }
}
