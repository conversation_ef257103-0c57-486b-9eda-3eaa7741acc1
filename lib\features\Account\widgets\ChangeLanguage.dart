import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/features/Account/controller.dart';
import 'package:shop_app/gen/assets.gen.dart';

class ChangeLanguage extends StatelessWidget {
  const ChangeLanguage({
    super.key,
    required this.controller,
  });

  final AccountPageController controller;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            StyleRepo.purple,
            StyleRepo.violet,
            StyleRepo.indigo,
            StyleRepo.lightBlue,
            StyleRepo.metaBlue,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: StyleRepo.purple.withValues(alpha: 0.4),
            blurRadius: 15,
            offset: const Offset(0, 6),
          ),
          BoxShadow(
            color: StyleRepo.violet.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        children: [
          Assets.icons.file.svg(height: 40, width: 40),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  tr(LocaleKeys.language),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: StyleRepo.white,
                      ),
                ),
                const SizedBox(height: 4),
                Text(
                  context.locale.languageCode == 'ar'
                      ? tr(LocaleKeys.arabic)
                      : tr(LocaleKeys.english),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: StyleRepo.white.withValues(alpha: 0.8),
                      ),
                ),
              ],
            ),
          ),
          GestureDetector(
            onPanStart: (details) => controller.handlePanStart(),
            onPanUpdate: (details) => controller.handlePanUpdate(details),
            onPanEnd: (details) => controller.handlePanEnd(details, context),
            child: Container(
              width: 100,
              height: 42,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    StyleRepo.white.withValues(alpha: 0.9),
                    StyleRepo.white.withValues(alpha: 0.8),
                    StyleRepo.lightMetaBlue.withValues(alpha: 0.9),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: StyleRepo.white.withValues(alpha: 0.3),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: StyleRepo.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                  BoxShadow(
                    color: StyleRepo.white.withValues(alpha: 0.2),
                    blurRadius: 4,
                    offset: const Offset(0, -1),
                  ),
                ],
              ),
              child: Obx(() => Stack(
                    children: [
                      // خلفية النصوص
                      Positioned.fill(
                        child: Row(
                          children: [
                            Spacer(),
                            Expanded(
                              child: Center(
                                child: Text(
                                  Get.locale!.languageCode == 'ar' ? 'EN' : 'ع',
                                  style: Theme.of(context)
                                      .textTheme
                                      .labelMedium!
                                      .copyWith(color: StyleRepo.black),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      // الكرة المتحركة
                      Positioned(
                        left: controller.getSliderPosition(context),
                        top: 4,
                        child: AnimatedContainer(
                          duration: controller.isDragging.value
                              ? Duration.zero
                              : const Duration(milliseconds: 200),
                          curve: Curves.easeInOut,
                          width: 34,
                          height: 34,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                StyleRepo.metaBlue,
                                StyleRepo.facebookBlue,
                                StyleRepo.metaLightBlue,
                              ],
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                            ),
                            borderRadius: BorderRadius.circular(17),
                            boxShadow: [
                              BoxShadow(
                                color:
                                    StyleRepo.metaBlue.withValues(alpha: 0.4),
                                blurRadius:
                                    controller.isDragging.value ? 10 : 8,
                                offset: const Offset(0, 3),
                              ),
                              BoxShadow(
                                color: StyleRepo.white.withValues(alpha: 0.3),
                                blurRadius: 2,
                                offset: const Offset(0, -1),
                              ),
                            ],
                          ),
                          child: Center(
                            child: Text(
                              Get.locale!.languageCode == 'en' ? 'EN' : 'ع',
                              style: Theme.of(context)
                                  .textTheme
                                  .labelSmall!
                                  .copyWith(color: StyleRepo.white),
                            ),
                          ),
                        ),
                      ),
                    ],
                  )),
            ),
          ),
        ],
      ),
    );
  }
}
