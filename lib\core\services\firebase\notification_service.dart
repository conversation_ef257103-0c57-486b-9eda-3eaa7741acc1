import 'dart:convert';
import 'dart:developer';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shop_app/core/config/StatusOrder.dart';

// معالج الرسائل في الخلفية
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  log('معالجة رسالة في الخلفية: ${message.messageId}');

  // حفظ الإشعار في التخزين المحلي
  final storage = GetStorage();
  List<String> notifications =
      storage.read<List<String>>('notifications') ?? [];

  final notificationData = {
    'id': message.messageId ?? DateTime.now().millisecondsSinceEpoch.toString(),
    'title': message.notification?.title ?? 'إشعار جديد',
    'body': message.notification?.body ?? '',
    'data': message.data,
    'timestamp': DateTime.now().toIso8601String(),
    'isRead': false,
  };

  notifications.insert(0, jsonEncode(notificationData));
  await storage.write('notifications', notifications);
}

class NotificationService extends GetxController {
  static NotificationService get instance => Get.find<NotificationService>();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final GetStorage _storage = GetStorage();

  // قائمة الإشعارات المحلية
  final RxList<NotificationModel> _notifications = <NotificationModel>[].obs;
  List<NotificationModel> get notifications => _notifications;

  // عدد الإشعارات غير المقروءة
  RxInt get unreadCount => _notifications.where((n) => !n.isRead).length.obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeFirebase();
    await _loadLocalNotifications();
    await _setupMessageHandlers();
  }

  // تهيئة Firebase
  Future<void> _initializeFirebase() async {
    try {
      // طلب الأذونات
      NotificationSettings settings =
          await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      log('حالة الأذونات: ${settings.authorizationStatus}');

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        log('المستخدم منح الأذونات للإشعارات');
      } else {
        log('المستخدم رفض الأذونات للإشعارات');
      }

      // الحصول على FCM Token
      String? token = await _firebaseMessaging.getToken();
      log('FCM Token: $token');

      // حفظ التوكن في التخزين المحلي
      if (token != null) {
        await _storage.write('fcm_token', token);
      }
    } catch (e) {
      log('خطأ في تهيئة Firebase: $e');
    }
  }

  // إعداد معالجات الرسائل
  Future<void> _setupMessageHandlers() async {
    // معالج الرسائل عندما يكون التطبيق في المقدمة
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      log('رسالة مستلمة في المقدمة: ${message.notification?.title}');
      _handleForegroundMessage(message);
    });

    // معالج الرسائل عندما يتم النقر على الإشعار والتطبيق في الخلفية
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      log('تم فتح التطبيق من الإشعار: ${message.notification?.title}');
      _handleNotificationTap(message);
    });

    // التحقق من الرسائل عند فتح التطبيق
    RemoteMessage? initialMessage =
        await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      log('التطبيق تم فتحه من إشعار: ${initialMessage.notification?.title}');
      _handleNotificationTap(initialMessage);
    }

    // تعيين معالج الرسائل في الخلفية
    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
  }

  // معالجة الرسائل في المقدمة
  void _handleForegroundMessage(RemoteMessage message) {
    final notification = NotificationModel.fromFirebaseMessage(message);
    _addNotification(notification);

    // عرض إشعار محلي
    _showLocalNotification(notification);
  }

  // معالجة النقر على الإشعار
  void _handleNotificationTap(RemoteMessage message) {
    final notification = NotificationModel.fromFirebaseMessage(message);
    _addNotification(notification);

    // التنقل حسب نوع الإشعار
    _navigateBasedOnNotification(notification);
  }

  // عرض إشعار محلي (دالة عامة)
  void showLocalNotification(NotificationModel notification) {
    Get.snackbar(
      notification.title,
      notification.body,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.blue.withValues(alpha: 0.9),
      colorText: Colors.white,
      duration: const Duration(seconds: 4),
      margin: const EdgeInsets.all(16),
      borderRadius: 12,
      icon: const Icon(Icons.notifications, color: Colors.white),
      onTap: (_) => _navigateBasedOnNotification(notification),
    );
  }

  // عرض إشعار محلي (دالة خاصة)
  void _showLocalNotification(NotificationModel notification) {
    showLocalNotification(notification);
  }

  // التنقل حسب نوع الإشعار
  void _navigateBasedOnNotification(NotificationModel notification) {
    if (notification.data.containsKey('order_id')) {
      // التنقل إلى صفحة تفاصيل الطلب
      Get.toNamed('/order-details', arguments: notification.data['order_id']);
    } else if (notification.data.containsKey('type')) {
      switch (notification.data['type']) {
        case 'order_status':
          Get.toNamed('/my-orders');
          break;
        case 'promotion':
          Get.toNamed('/home');
          break;
        default:
          Get.toNamed('/notifications');
      }
    } else {
      Get.toNamed('/notifications');
    }
  }

  // إضافة إشعار جديد (دالة عامة)
  void addNotification(NotificationModel notification) {
    _notifications.insert(0, notification);
    _saveLocalNotifications();
  }

  // إضافة إشعار جديد (دالة خاصة)
  void _addNotification(NotificationModel notification) {
    addNotification(notification);
  }

  // تحميل الإشعارات المحلية
  Future<void> _loadLocalNotifications() async {
    try {
      List<String> notificationsJson =
          _storage.read<List<String>>('notifications') ?? [];

      _notifications.clear();
      for (String json in notificationsJson) {
        try {
          Map<String, dynamic> data = jsonDecode(json);
          _notifications.add(NotificationModel.fromJson(data));
        } catch (e) {
          log('خطأ في تحليل الإشعار: $e');
        }
      }
    } catch (e) {
      log('خطأ في تحميل الإشعارات: $e');
    }
  }

  // حفظ الإشعارات المحلية
  Future<void> _saveLocalNotifications() async {
    try {
      List<String> notificationsJson = _notifications
          .map((notification) => jsonEncode(notification.toJson()))
          .toList();

      await _storage.write('notifications', notificationsJson);
    } catch (e) {
      log('خطأ في حفظ الإشعارات: $e');
    }
  }

  // تحديد إشعار كمقروء
  void markAsRead(String notificationId) {
    int index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
      _saveLocalNotifications();
    }
  }

  // تحديد جميع الإشعارات كمقروءة
  void markAllAsRead() {
    for (int i = 0; i < _notifications.length; i++) {
      _notifications[i] = _notifications[i].copyWith(isRead: true);
    }
    _saveLocalNotifications();
  }

  // حذف إشعار
  void deleteNotification(String notificationId) {
    _notifications.removeWhere((n) => n.id == notificationId);
    _saveLocalNotifications();
  }

  // مسح جميع الإشعارات
  void clearAllNotifications() {
    _notifications.clear();
    _saveLocalNotifications();
  }

  // الحصول على FCM Token
  Future<String?> getFCMToken() async {
    return await _firebaseMessaging.getToken();
  }

  // الاشتراك في موضوع
  Future<void> subscribeToTopic(String topic) async {
    await _firebaseMessaging.subscribeToTopic(topic);
    log('تم الاشتراك في الموضوع: $topic');
  }

  // إلغاء الاشتراك في موضوع
  Future<void> unsubscribeFromTopic(String topic) async {
    await _firebaseMessaging.unsubscribeFromTopic(topic);
    log('تم إلغاء الاشتراك في الموضوع: $topic');
  }
}

// نموذج الإشعار
class NotificationModel {
  final String id;
  final String title;
  final String body;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final bool isRead;
  final NotificationType type;

  NotificationModel({
    required this.id,
    required this.title,
    required this.body,
    required this.data,
    required this.timestamp,
    this.isRead = false,
    this.type = NotificationType.general,
  });

  factory NotificationModel.fromFirebaseMessage(RemoteMessage message) {
    return NotificationModel(
      id: message.messageId ?? DateTime.now().millisecondsSinceEpoch.toString(),
      title: message.notification?.title ?? 'إشعار جديد',
      body: message.notification?.body ?? '',
      data: message.data,
      timestamp: DateTime.now(),
      type: _getNotificationTypeFromData(message.data),
    );
  }

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'],
      title: json['title'],
      body: json['body'],
      data: Map<String, dynamic>.from(json['data'] ?? {}),
      timestamp: DateTime.parse(json['timestamp']),
      isRead: json['isRead'] ?? false,
      type: NotificationType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => NotificationType.general,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
      'isRead': isRead,
      'type': type.toString(),
    };
  }

  NotificationModel copyWith({
    String? id,
    String? title,
    String? body,
    Map<String, dynamic>? data,
    DateTime? timestamp,
    bool? isRead,
    NotificationType? type,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      data: data ?? this.data,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      type: type ?? this.type,
    );
  }

  static NotificationType _getNotificationTypeFromData(
      Map<String, dynamic> data) {
    if (data.containsKey('order_id') || data['type'] == 'order_status') {
      return NotificationType.orderUpdate;
    } else if (data['type'] == 'promotion') {
      return NotificationType.promotion;
    }
    return NotificationType.general;
  }
}

// أنواع الإشعارات
enum NotificationType {
  general,
  orderUpdate,
  promotion,
}
