import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/features/MyOrders/Model/OderModel.dart';
import 'package:shop_app/features/MyOrders/controller.dart';
import 'package:shop_app/features/MyOrders/widgets/buildProgressSteps.dart';

class buildProgressBar extends StatelessWidget {
  final OrderModel order;
  const buildProgressBar({super.key, required this.order});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<MyOrdersPageController>();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 5),
        if (controller.isDelivery == true && order.customer != null)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Name with icon
              Row(
                children: [
                  const Icon(Icons.person, size: 20, color: Colors.grey),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      order.customer!.name,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              // Address with icon
              Row(
                children: [
                  const Icon(Icons.location_on, size: 20, color: Colors.grey),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      order.address,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
            ],
          ),
        SizedBox(
          height: 10,
        ),
        Text(
          tr(LocaleKeys.OrderStatus),
          maxLines: 1,
          style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                overflow: TextOverflow.ellipsis,
              ),
        ),
        SizedBox(
          height: 10,
        ),
        buildProgressSteps(order: order),
      ],
    );
  }
}
