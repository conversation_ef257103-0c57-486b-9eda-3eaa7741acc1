import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/config/StatusOrder.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/features/MyOrders/controller.dart';

class buildProgressStep extends StatelessWidget {
  const buildProgressStep({
    super.key,
    required this.status,
    required this.label,
    required this.stepStatus,
    required this.index,
    required this.totalSteps,
  });

  final OrderStatus status;
  final String label;
  final OrderStatus stepStatus;
  final int index;
  final int totalSteps;

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<MyOrdersPageController>();

    final isActive = controller.isStepActive(status, stepStatus);
    final isCompleted = controller.isStepCompleted(status, stepStatus);

    Color stepColor;
    if (isCompleted) {
      stepColor = StyleRepo.green;
    } else if (isActive) {
      stepColor = status.color;
    } else {
      stepColor = StyleRepo.lightGrey;
    }

    return Expanded(
      child: Column(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: isCompleted || isActive ? stepColor : StyleRepo.white,
              shape: BoxShape.circle,
              border: Border.all(
                color: stepColor,
                width: 2,
              ),
              boxShadow: isActive || isCompleted
                  ? [
                      BoxShadow(
                        color: stepColor.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
            child: Icon(
              status.icon,
              size: 16,
              color: isCompleted || isActive ? StyleRepo.white : stepColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            maxLines: 2,
            style: TextStyle(
              fontSize: 10,
              fontWeight:
                  isActive || isCompleted ? FontWeight.w600 : FontWeight.w400,
              color: isActive || isCompleted ? stepColor : StyleRepo.darkGrey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
