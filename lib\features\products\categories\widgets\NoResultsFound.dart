import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/style/repo.dart';

class NoResultsFound extends StatelessWidget {
  final String Errore;

  const NoResultsFound({super.key, required this.Errore});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.search_off, size: 64, color: StyleRepo.darkGrey),
        SizedBox(height: 16),
        Text(
          Errore,
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 8),
        Text(
          tr(LocaleKeys.Try_Different_Keywords),
          style: TextStyle(color: StyleRepo.darkGrey),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
