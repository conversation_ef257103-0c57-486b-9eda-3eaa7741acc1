import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/config/StatusOrder.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/features/MyOrders/Model/OderModel.dart';
import 'package:shop_app/features/MyOrders/controller.dart';
import 'package:shop_app/features/MyOrders/widgets/buildOrderItem.dart';
import 'package:shop_app/features/MyOrders/widgets/buildReceivedButton.dart';

class buildExpandedContent extends StatelessWidget {
  final OrderStatus status;
  final List<ProductsOrder> productsOrder;

  final Function(int orderNumber, bool IsCancel)? onMarkAsReceived;
  final int orderNumber;
  final double TotalPrice;

  const buildExpandedContent({
    super.key,
    required this.status,
    required this.onMarkAsReceived,
    required this.orderNumber,
    required this.productsOrder,
    required this.TotalPrice,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<MyOrdersPageController>();

    return Container(
      decoration: BoxDecoration(
        color: StyleRepo.lightMetaBlue.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Divider
            Container(
              height: 1,
              width: double.infinity,
              color: StyleRepo.lightGrey.withValues(alpha: 0.3),
              margin: const EdgeInsets.only(bottom: 20),
            ),

            // Order Details Title
            Center(
              child: Text(
                tr(LocaleKeys.Order_details),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: StyleRepo.metaBlue,
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Order Items (Mock Data)
            SingleChildScrollView(
              child: Column(
                children: List.generate(productsOrder.length, (index) {
                  final product = productsOrder[index];
                  return buildOrderItem(
                    name: product.name,
                    quantity: product.quantity,
                    price: product.price.toDouble(),
                  );
                }),
              ),
            ),

            const SizedBox(height: 16),

            // Total
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    StyleRepo.metaBlue.withValues(alpha: 0.1),
                    StyleRepo.metaSkyBlue.withValues(alpha: 0.1)
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: StyleRepo.metaBlue.withValues(alpha: 0.2),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    tr(LocaleKeys.Total),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: StyleRepo.metaBlue,
                    ),
                  ),
                  Text(
                    TotalPrice.toString() + " \$",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: StyleRepo.metaBlue,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // Action Button for Processing Orders
            if (status == OrderStatus.outForDelivery &&
                controller.isDelivery == false)
              buildReceivedButton(
                onMarkAsReceived: onMarkAsReceived,
                orderNumber: orderNumber,
                IsCancel: false,
              ),
            if (status == OrderStatus.pending && controller.isDelivery == false)
              buildReceivedButton(
                onMarkAsReceived: onMarkAsReceived,
                orderNumber: orderNumber,
                IsCancel: true,
              ),
          ],
        ),
      ),
    );
  }
}
