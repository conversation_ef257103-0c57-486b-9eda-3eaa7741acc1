import 'package:carousel_slider/carousel_slider.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/services/rest_api/constants/end_points.dart';
import 'package:shop_app/core/services/state_management/widgets/obs_widget.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/core/widgets/image.dart';
import 'package:shop_app/gen/assets.gen.dart';
import 'controller.dart';

class ProductDetailsPage extends StatelessWidget {
  ProductDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(ProductDetailsPageController());
    return Scaffold(
      body: // باقي الصفحة
          MediaQuery.removePadding(
        context: context,
        removeTop: true,
        child: ListView(
          children: [
            /// ✅ الصور مع السلايدر
            ObsVariableBuilder(
              obs: controller.product,
              builder: (context, product) {
                return Container(
                  height: MediaQuery.of(context).size.height * 0.3,
                  decoration: BoxDecoration(
                    color: StyleRepo.gray,
                    borderRadius: const BorderRadius.vertical(
                      bottom: Radius.circular(30),
                    ),
                  ),
                  child: Stack(
                    children: [
                      /// ✅ Carousel للصور
                      CarouselSlider(
                        options: CarouselOptions(
                          viewportFraction: 1,
                          enlargeCenterPage: false,
                          autoPlay: true,
                          aspectRatio: 16 / 11,
                          onPageChanged: (index, _) {
                            controller.currentAd = index;
                          },
                        ),
                        items: List.generate(
                          product.images?.length ?? 0,
                          (index) => Padding(
                            padding: const EdgeInsets.symmetric(
                                vertical: 40, horizontal: 60),
                            child: AppImage(
                              height: 100,
                              path: product.images!.isNotEmpty
                                  ? EndPoints.baseUrl +
                                      product.images![index].image
                                  : '',
                              type: ImageType.CachedNetwork,
                              width: double.infinity,
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ),

                      /// ✅ مؤشر الصور
                      Positioned(
                        bottom: 10,
                        left: 0,
                        right: 0,
                        child: Center(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: List.generate(
                              product.images?.length ?? 0,
                              (index) => Obx(() => AnimatedContainer(
                                    duration: 300.milliseconds,
                                    margin: const EdgeInsets.symmetric(
                                        horizontal: 4),
                                    height: 6,
                                    width:
                                        controller.currentAd == index ? 15 : 6,
                                    decoration: BoxDecoration(
                                      shape: controller.currentAd == index
                                          ? BoxShape.rectangle
                                          : BoxShape.circle,
                                      color: controller.currentAd == index
                                          ? StyleRepo.green
                                          : StyleRepo.lightGrey,
                                    ),
                                  )),
                            ),
                          ),
                        ),
                      ),

                      /// ✅ زر الرجوع والمشاركة
                      Positioned(
                        top: 40,
                        left: 10,
                        right: 20,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            IconButton(
                              icon:
                                  Assets.icons.back.svg(color: StyleRepo.black),
                              onPressed: () => Get.back(),
                            ),
                            Assets.icons.share.svg()
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),

            ObsVariableBuilder(
                obs: controller.product,
                builder: (context, product) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 10,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              product.name,
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                            Assets.icons.love.svg(color: StyleRepo.darkGrey)
                          ],
                        ),
                        SizedBox(height: 16),
                        Text(
                          product.description!,
                          style: TextStyle(
                            color: StyleRepo.darkGrey,
                          ),
                        ),
                      ],
                    ),
                  );
                }),

            SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                IconButton(
                  onPressed: () {
                    if (controller.quantity != 0) {
                      controller.quantity--;
                    }
                  },
                  icon: Assets.icons.minus.svg(width: 20),
                ),
                Container(
                  width: 45,
                  height: 45,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(
                      Radius.circular(17),
                    ),
                    border: Border.all(
                      color: StyleRepo.lightGrey,
                      width: 2.0,
                    ),
                  ),
                  child: Center(
                    child: Obx(
                      () => Text(
                        controller.quantity.toString(),
                        style: TextStyle(color: StyleRepo.black, fontSize: 20),
                      ),
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () {
                    controller.quantity++;
                  },
                  icon: Assets.icons.add.svg(
                      colorFilter: ColorFilter.mode(
                          Theme.of(context).primaryColor, BlendMode.srcIn)),
                ),
                Spacer(),
                Obx(
                  () => Text(
                    "${controller.price.value * controller.quantity.value} \$  ",
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),

            // Notes Section
            Container(
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: StyleRepo.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: StyleRepo.lightGrey,
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: StyleRepo.lightGrey.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.edit_note_rounded,
                        color: StyleRepo.green,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        "ملاحظات المنتج",
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: StyleRepo.darkGrey,
                                ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  TextField(
                    controller: controller.notes,
                    maxLines: 3,
                    maxLength: 200,
                    decoration: InputDecoration(
                      hintText: "أضف ملاحظات خاصة بهذا المنتج...",
                      hintStyle: TextStyle(
                        color: StyleRepo.lightGrey,
                        fontSize: 14,
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: StyleRepo.lightGrey,
                          width: 1,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: StyleRepo.green,
                          width: 2,
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: StyleRepo.lightGrey,
                          width: 1,
                        ),
                      ),
                      contentPadding: const EdgeInsets.all(12),
                      counterStyle: TextStyle(
                        color: StyleRepo.lightGrey,
                        fontSize: 12,
                      ),
                    ),
                    style: TextStyle(
                      color: StyleRepo.black,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(30),
        child: Obx(
          () => ElevatedButton(
            style: ElevatedButton.styleFrom(
              minimumSize: Size(120, 60),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(19),
              ),
              backgroundColor: controller.quantity == 0
                  ? StyleRepo.darkGrey // لون الزر عند التعطيل
                  : Theme.of(context).primaryColor,
            ),
            onPressed: controller.quantity == 0 ? () {} : controller.confirm,
            child: Text(
              tr(LocaleKeys.Add_To_Cart),
              style: TextStyle(fontSize: 20, color: StyleRepo.white),
            ),
          ),
        ),
      ),
    );
  }
}
