// ignore_for_file: constant_identifier_names, non_constant_identifier_names

abstract class EndPoints {
  //##########  Base Url  ##########
  static const String baseUrl =
      //   'https://b4f84ebd-016e-4fed-9492-9e3bd664d12b.mock.pstmn.io/';
      //  'http://82.137.244.167:3000/';
      'http://192.168.3.15:3000/';
  //Auth
  static const login = "customers/login";
  static const home = "customers/home";
  // Content
  static const categories = "customers/categories";
  static const Fcm_token = "customers/save-firebase-token";
  static const DeliveryCurrent = "customers/delivery";
  static const ordersCurrent = "customers/orders";
  static const DeliveryPrevious = "customers/delivery/completed";
  static const ordersPrevious = "customers/orders/completed";
  static const profile = "customers/profile";
  static order(int id) => "customers/orders/$id";
  static Cancel(int id) => "customers/orders/$id/cancel";
  static const checkout = "customers/checkout";
  static const products = "customers/products";
  static product(int id) => "customers/products/$id";
  static categorie(int id) => "customers/categories/$id";
}
