import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/routes/routes.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/features/Account/widgets/menucard.dart';
import 'package:shop_app/gen/assets.gen.dart';

class MenuCards extends StatelessWidget {
  const MenuCards({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      child: Column(
        children: [
          // First Row
          Row(
            children: [
              Expanded(
                child: buildMenuCard(
                  context,
                  Assets.icons.orders.svg(),
                  tr(LocaleKeys.Orders),
                  StyleRepo.blue,
                  () => Get.toNamed(Pages.MyOrders.value, arguments: false),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: buildMenuCard(
                  context,
                  Assets.icons.myDetails.svg(),
                  tr(LocaleKeys.My_Details),
                  StyleRepo.purple,
                  () {},
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Second Row
          Row(
            children: [
              Expanded(
                child: buildMenuCard(
                  context,
                  Assets.icons.deliveryAddress.svg(),
                  tr(LocaleKeys.Delivery_Address),
                  StyleRepo.orange,
                  () => Get.toNamed(Pages.location_picker.value),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: buildMenuCard(
                  context,
                  Assets.icons.paymentMethods.svg(),
                  tr(LocaleKeys.Payment_Methods),
                  StyleRepo.teal,
                  () {},
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Third Row
          Row(
            children: [
              Expanded(
                child: buildMenuCard(
                  context,
                  Assets.icons.about.svg(),
                  tr(LocaleKeys.About),
                  StyleRepo.cyan,
                  () => Get.toNamed(Pages.about_us.value),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: buildMenuCard(
                  context,
                  Assets.icons.notification.svg(),
                  tr(LocaleKeys.Notifications),
                  StyleRepo.indigo,
                  () {},
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Fourth Row
          Row(
            children: [
              const SizedBox(width: 12),
            ],
          ),
        ],
      ),
    );
  }
}
