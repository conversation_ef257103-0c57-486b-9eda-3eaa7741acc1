import 'dart:convert';

class ProfileModel {
  int id;
  String name;
  String barcode;
  String image;
  String? phoneNumber;
  String? address;
  String? city;
  String? region;
  String? notes;
  int orderCount;

  ProfileModel({
    required this.id,
    required this.name,
    required this.barcode,
    required this.image,
    this.phoneNumber,
    this.address,
    this.city,
    this.region,
    this.notes,
    required this.orderCount,
  });

  factory ProfileModel.fromRawJson(String str) =>
      ProfileModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ProfileModel.fromJson(Map<String, dynamic> json) => ProfileModel(
        id: json["id"],
        name: json["name"],
        barcode: json["barcode"],
        image: json["image"],
        phoneNumber: json["phoneNumber"],
        address: json["address"],
        city: json["city"],
        region: json["region"],
        notes: json["notes"],
        orderCount: json["orderCount"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "barcode": barcode,
        "image": image,
        "phoneNumber": phoneNumber,
        "address": address,
        "city": city,
        "region": region,
        "notes": notes,
        "orderCount": orderCount,
      };
}
