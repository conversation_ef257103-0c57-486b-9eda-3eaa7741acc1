import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/features/home/<USER>/categories.dart';
import 'package:shop_app/features/products/cart/controller.dart';
import 'controller.dart';
import 'widgets/ads.dart';
import 'widgets/products_section.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    Get.put(HomePageController());
    Get.put(CartPageController());

    return ListView(
      children: [
        AdsWidget(),
        SizedBox(height: 16),
        ProductsSection(),
        SizedBox(height: 16),
        CategoriesSection(),
        SizedBox(
          height: 16,
        )
      ],
    );
  }
}
