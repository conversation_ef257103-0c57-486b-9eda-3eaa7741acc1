import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/style/repo.dart';
import 'controller.dart';

class LocationDetailsPage extends StatelessWidget {
  const LocationDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(LocationDetailsController());

    return Scaffold(
      backgroundColor: StyleRepo.lightMetaBlue,
      appBar: AppBar(
        backgroundColor: StyleRepo.indigo,
        foregroundColor: const Color.fromRGBO(252, 252, 252, 1),
        flexibleSpace: SafeArea(
          child: Center(
            child: Text(
              tr(LocaleKeys.my_location),
              style: Theme.of(context)
                  .textTheme
                  .titleLarge!
                  .copyWith(color: StyleRepo.sandyBrown),
            ),
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: StyleRepo.black),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Header Section with Meta gradient
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    StyleRepo.indigo,
                    StyleRepo.lightMetaBlue,
                  ],
                ),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(30),
                  bottomRight: Radius.circular(30),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    // Location Icon
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: StyleRepo.white.withValues(alpha: 0.2),
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: StyleRepo.white.withValues(alpha: 0.3),
                          width: 2,
                        ),
                      ),
                      child: Icon(
                        Icons.location_on,
                        size: 40,
                        color: StyleRepo.white,
                      ),
                    ),
                    SizedBox(height: 16),
                    Text(
                      tr(LocaleKeys.location_confirmed_successfully),
                      style: Theme.of(context)
                          .textTheme
                          .titleMedium!
                          .copyWith(color: StyleRepo.black),
                    ),
                    SizedBox(height: 8),
                  ],
                ),
              ),
            ),

            SizedBox(height: 20),

            // Location Details Card
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: StyleRepo.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: StyleRepo.metaBlue.withValues(alpha: 0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Address Section
                      Row(
                        children: [
                          Container(
                            width: 50,
                            height: 50,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  StyleRepo.metaBlue,
                                  StyleRepo.lightMetaBlue
                                ],
                              ),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: Icon(
                              Icons.home_outlined,
                              color: StyleRepo.white,
                              size: 24,
                            ),
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  tr(LocaleKeys.address),
                                  style: TextStyle(
                                    color: StyleRepo.darkGrey,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                SizedBox(height: 4),
                                Text(
                                  controller.address,
                                  style: TextStyle(
                                    color: StyleRepo.black,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                )
                              ],
                            ),
                          ),
                        ],
                      ),

                      SizedBox(height: 20),
                      Divider(
                          color: StyleRepo.lightGrey.withValues(alpha: 0.5)),
                      SizedBox(height: 20),

                      // Additional Details Section
                      Text(
                        tr(LocaleKeys.additional_details),
                        style: TextStyle(
                          color: StyleRepo.black,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 16),

                      // Building Number
                      TextField(
                        controller: controller.NotesAddress,
                        maxLines: 4,
                        decoration: InputDecoration(
                          hintText: tr(LocaleKeys.detailed_address_hint),
                          hintStyle: TextStyle(
                            color: StyleRepo.lightGrey,
                            fontSize: 14,
                          ),
                          filled: true,
                          fillColor:
                              StyleRepo.lightMetaBlue.withValues(alpha: 0.1),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none,
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: StyleRepo.metaBlue,
                              width: 2,
                            ),
                          ),
                        ),
                        style: TextStyle(
                          color: StyleRepo.black,
                          fontSize: 16,
                        ),
                      ),

                      SizedBox(height: 16),
                    ],
                  ),
                ),
              ),
            ),

            SizedBox(height: 30),

            SizedBox(height: 30),
          ],
        ),
      ),
      bottomNavigationBar: // Action Buttons

          SizedBox(
        height: 130,
        child: ElevatedButton(
          onPressed: () => controller.sendLocationDetails(),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.transparent,
            shadowColor: Colors.transparent,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
          ),
          child: Container(
            height: 60,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  StyleRepo.lightGrey,
                  StyleRepo.indigo,
                  StyleRepo.indigo
                ],
              ),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.send_rounded,
                  color: StyleRepo.black,
                  size: 24,
                ),
                SizedBox(width: 12),
                Text(
                  tr(LocaleKeys.send_location_details),
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
