import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart' hide MultipartFile, FormData;
import 'package:image_picker/image_picker.dart';
import 'package:shop_app/core/services/state_management/obs.dart';
import 'package:shop_app/features/Account/Models/ProfileModel.dart';
import '../../core/services/rest_api/rest_api.dart';
import 'package:http_parser/http_parser.dart';

class EditAccountPageController extends GetxController {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  late TextEditingController name, barcode, phoneNumber;

  Rx<File?> image = Rx<File?>(null); // صورة محلية
  RxString networkImage = "".obs; // رابط الصورة من السيرفر

  ObsVar<ProfileModel> profileModel = ObsVar(null);

  @override
  void onInit() {
    name = TextEditingController();
    barcode = TextEditingController();
    phoneNumber = TextEditingController();
    fetchProfile();
    super.onInit();
  }

  @override
  void onClose() {
    name.dispose();
    barcode.dispose();
    phoneNumber.dispose();
    super.onClose();
  }

  Future<void> pickImage() async {
    XFile? picked = await ImagePicker().pickImage(source: ImageSource.gallery);
    if (picked != null) {
      image.value = File(picked.path);
    }
  }

  Future<void> fetchProfile() async {
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.profile,
        fromJson: ProfileModel.fromJson,
      ),
    );

    if (response.success) {
      profileModel.value = response.data;
      name.text = profileModel.value!.name;
      barcode.text = profileModel.value!.barcode;
      phoneNumber.text = profileModel.value!.phoneNumber ?? "";
      networkImage.value =
          EndPoints.baseUrl + (profileModel.value?.image ?? "");
    } else {
      profileModel.error = response.message;
    }
  }

  confirm() async {
    if (!formKey.currentState!.validate()) return;

    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.profile,
        method: RequestMethod.Put,
        body: FormData.fromMap({
          "name": name.text,
          "barcode": barcode.text,
          "phoneNumber": phoneNumber.text,
          if (image.value != null)
            "image": await MultipartFile.fromFile(
              image.value!.path,
              contentType: MediaType('image', 'png'),
            ),
        }),
      ),
    );

    if (response.success) {
      Get.back(result: true);

      Get.snackbar("تم تعديل البروفايل", "تم تعديل بروفايل السيد ${name.text}");
    } else {}
  }
}
