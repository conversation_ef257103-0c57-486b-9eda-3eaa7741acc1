import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/config/StatusOrder.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/features/MyOrders/controller.dart';

class buildProgressConnector extends StatelessWidget {
  final int index;
  final bool isActive;
  final OrderStatus status;

  const buildProgressConnector({
    super.key,
    required this.index,
    required this.isActive,
    required this.status, // تأكد أنه موجود
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<MyOrdersPageController>();
    final isConnectorActive = controller.isConnectorActive(status, index);

    return Container(
      height: 4,
      width: 35,
      margin: const EdgeInsets.only(top: 15),
      decoration: BoxDecoration(
        gradient: isConnectorActive
            ? LinearGradient(
                colors: [StyleRepo.metaBlue, StyleRepo.metaSkyBlue],
              )
            : null,
        color: isConnectorActive ? null : StyleRepo.lightGrey,
        borderRadius: BorderRadius.circular(1),
      ),
    );
  }
}
