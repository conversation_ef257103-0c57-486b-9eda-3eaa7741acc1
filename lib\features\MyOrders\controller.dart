// my_orders_page_controller.dart
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/config/StatusOrder.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/services/pagination/controller.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/features/Notification/controller.dart';
import '../../core/services/rest_api/rest_api.dart';

class MyOrdersPageController extends GetxController {
  final RxMap<int, bool> expandedStates = <int, bool>{}.obs;
  final notificationController = Get.find<NotificationController>();
  late bool isDelivery;
  late int id;
  bool _isProcessingOrder = false;
  // دالة تبديل حالة التوسيع
  void toggleExpansion(int orderNumber) {
    final current = expandedStates[orderNumber] ?? false;
    expandedStates[orderNumber] = !current;
    expandedStates.refresh(); // إجبار التحديث
  }

  bool isStepActive(OrderStatus currentStatus, OrderStatus stepStatus) {
    return currentStatus == stepStatus;
  }

  bool isStepCompleted(OrderStatus currentStatus, OrderStatus stepStatus) {
    if (currentStatus == OrderStatus.cancelled ||
        currentStatus == OrderStatus.rejected) {
      return stepStatus == OrderStatus.pending;
    }

    final stepOrder = {
      OrderStatus.pending: 0,
      OrderStatus.processing: 1,
      OrderStatus.outForDelivery: 2,
      OrderStatus.completed: 3,
    };

    final currentOrder = stepOrder[currentStatus];
    final compareOrder = stepOrder[stepStatus];

    if (currentOrder == null || compareOrder == null) {
      return false;
    }

    return compareOrder < currentOrder;
  }

  bool isConnectorActive(OrderStatus status, int index) {
    if (status == OrderStatus.cancelled || status == OrderStatus.rejected) {
      return false;
    }

    switch (index) {
      case 0: // Between pending and processing
        return status.index >= OrderStatus.processing.index;
      case 1: // Between processing and outForDelivery
        return status.index >= OrderStatus.outForDelivery.index;
      case 2: // Between outForDelivery and completed
        return status.index >= OrderStatus.completed.index;
      default:
        return false;
    }
  }

  late PaginationController pagerCurrentController, pagerPreviousController;

  Future<ResponseModel> fetchCurrentData(int page, CancelToken cancel) async {
    ResponseModel response = await APIService.instance.request(
      Request(
          endPoint:
              isDelivery ? EndPoints.DeliveryCurrent : EndPoints.ordersCurrent,
          params: {"page": page},
          cancelToken: cancel,
          getdata: "orders"),
    );

    return response;
  }

  Future<ResponseModel> fetchPreviousData(int page, CancelToken cancel) async {
    ResponseModel response = await APIService.instance.request(
      Request(
          endPoint: isDelivery
              ? EndPoints.DeliveryPrevious
              : EndPoints.ordersPrevious,
          params: {"page": page},
          cancelToken: cancel,
          getdata: "orders"),
    );

    return response;
  }

  comleteOrCanceldOrders(int id, bool IsCancel) async {
    // منع التداخل في العمليات
    if (_isProcessingOrder) {
      return;
    }

    _isProcessingOrder = true;

    try {
      ResponseModel response = await APIService.instance.request(
        Request(
          endPoint: IsCancel ? EndPoints.Cancel(id) : EndPoints.order(id),
          method: RequestMethod.Post,
        ),
      );

      if (response.success) {
        toggleExpansion(id);
        pagerCurrentController.refreshData();
        pagerPreviousController.refreshData();
        Get.snackbar(
          tr(LocaleKeys.success),
          IsCancel
              ? tr(LocaleKeys.Cancel_order_no) + " #$id"
              : tr(LocaleKeys.Order_receipt_confirmed) + " #$id",
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: StyleRepo.green,
          colorText: StyleRepo.white,
          duration: Duration(seconds: 1),
        );
      } else {
        toggleExpansion(id);
        Get.snackbar(
          tr(LocaleKeys.error),
          response.message,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: StyleRepo.red,
          colorText: StyleRepo.white,
          duration: Duration(seconds: 1),
        );
      }
    } finally {
      _isProcessingOrder = false;
    }
  }

  @override
  void onInit() {
    super.onInit();
    isDelivery = Get.arguments;
  }
}
