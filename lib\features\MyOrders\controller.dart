// my_orders_page_controller.dart
import 'package:easy_localization/easy_localization.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/config/StatusOrder.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/services/state_management/obs.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/features/MyOrders/Model/OderModel.dart';

import '../../core/services/rest_api/rest_api.dart';

class MyOrdersPageController extends GetxController {
  final RxMap<int, bool> expandedStates = <int, bool>{}.obs;
  ObsList<OrderModel> Myorder = ObsList([]);
  late bool isDelivery;
  // دالة تبديل حالة التوسيع
  void toggleExpansion(int orderNumber) {
    final current = expandedStates[orderNumber] ?? false;
    expandedStates[orderNumber] = !current;
  }

  bool isStepActive(OrderStatus currentStatus, OrderStatus stepStatus) {
    return currentStatus == stepStatus;
  }

  bool isStepCompleted(OrderStatus currentStatus, OrderStatus stepStatus) {
    if (currentStatus == OrderStatus.cancelled ||
        currentStatus == OrderStatus.rejected) {
      return stepStatus == OrderStatus.pending;
    }

    final stepOrder = {
      OrderStatus.pending: 0,
      OrderStatus.processing: 1,
      OrderStatus.outForDelivery: 2,
      OrderStatus.completed: 3,
    };

    final currentOrder = stepOrder[currentStatus];
    final compareOrder = stepOrder[stepStatus];

    if (currentOrder == null || compareOrder == null) {
      return false;
    }

    return compareOrder < currentOrder;
  }

  bool isConnectorActive(OrderStatus status, int index) {
    if (status == OrderStatus.cancelled || status == OrderStatus.rejected) {
      return false;
    }

    switch (index) {
      case 0: // Between pending and processing
        return status.index >= OrderStatus.processing.index;
      case 1: // Between processing and outForDelivery
        return status.index >= OrderStatus.outForDelivery.index;
      case 2: // Between outForDelivery and completed
        return status.index >= OrderStatus.completed.index;
      default:
        return false;
    }
  }

  fetchMyOrders() async {
    ResponseModel response = await APIService.instance.request(
      Request(
          endPoint: isDelivery ? EndPoints.Delivery : EndPoints.orders,
          fromJson: OrderModel.fromJson,
          getdata: isDelivery ? null : "orders"),
    );

    if (response.success) {
      Myorder.value = response.data;
      Myorder.refresh();
      if (Myorder.value!.isEmpty) {
        Myorder.error = tr(LocaleKeys.No_Results_Found);
      }
    } else {
      Myorder.error = response.message;
    }
  }

  comleteOrders(int id) async {
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.order(id),
        method: RequestMethod.Post,
      ),
    );

    if (response.success) {
      await fetchMyOrders();
      toggleExpansion(id);
      Myorder.refresh();
      Get.snackbar(
        tr(LocaleKeys.success),
        tr(LocaleKeys.Order_receipt_confirmed) + " #$id",
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: StyleRepo.green,
        colorText: StyleRepo.white,
        duration: Duration(seconds: 2),
      );
    } else {
      Myorder.error = response.message;
    }
  }

  List<OrderModel> filteredCurrentOrders(List<OrderModel> OffersProduct) {
    final filteredOrders = OffersProduct.where((order) {
      final status = OrderStatusParser.fromString(order.status);
      return status == OrderStatus.pending ||
          status == OrderStatus.outForDelivery ||
          status == OrderStatus.processing;
    }).toList();
    return filteredOrders;
  }

  List<OrderModel> filteredPreviousOrders(List<OrderModel> OffersProduct) {
    final filteredOrders = OffersProduct.where((order) {
      final status = OrderStatusParser.fromString(order.status);
      return status == OrderStatus.completed || status == OrderStatus.cancelled;
    }).toList();
    return filteredOrders;
  }

  @override
  void onInit() {
    isDelivery = Get.arguments;
    fetchMyOrders();
    super.onInit();
  }
}
// order_model.dart
