import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/services/firebase/notification_service.dart';
import 'package:shop_app/core/style/repo.dart';

class NotificationsController extends GetxController {
  // الحصول على خدمة الإشعارات
  NotificationService get notificationService => NotificationService.instance;

  @override
  void onInit() {
    super.onInit();
    // تحديث الإشعارات عند فتح الصفحة
    refreshNotifications();
  }

  // تحديث الإشعارات
  Future<void> refreshNotifications() async {
    // يمكن إضافة منطق لجلب الإشعارات من الخادم هنا
    // حالياً نعتمد على الإشعارات المحلية فقط
    await Future.delayed(const Duration(milliseconds: 500));
  }

  // التعامل مع النقر على الإشعار
  void onNotificationTap(NotificationModel notification) {
    // تحديد الإشعار كمقروء
    if (!notification.isRead) {
      notificationService.markAsRead(notification.id);
    }

    // التنقل حسب نوع الإشعار
    _navigateBasedOnNotification(notification);
  }

  // التنقل حسب نوع الإشعار
  void _navigateBasedOnNotification(NotificationModel notification) {
    switch (notification.type) {
      case NotificationType.orderUpdate:
        if (notification.data.containsKey('order_id')) {
          // التنقل إلى تفاصيل الطلب
          Get.toNamed('/order-details', arguments: notification.data['order_id']);
        } else {
          // التنقل إلى صفحة الطلبات
          Get.toNamed('/my-orders');
        }
        break;
      case NotificationType.promotion:
        // التنقل إلى الصفحة الرئيسية أو صفحة العروض
        Get.toNamed('/home');
        break;
      case NotificationType.general:
      default:
        // عرض تفاصيل الإشعار
        _showNotificationDetails(notification);
        break;
    }
  }

  // عرض تفاصيل الإشعار
  void _showNotificationDetails(NotificationModel notification) {
    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(
              _getNotificationIcon(notification.type),
              color: _getNotificationColor(notification.type),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                notification.title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: StyleRepo.black,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              notification.body,
              style: TextStyle(
                fontSize: 14,
                color: StyleRepo.darkGrey,
                height: 1.5,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: StyleRepo.lightGrey,
                ),
                const SizedBox(width: 4),
                Text(
                  _formatNotificationTime(notification.timestamp),
                  style: TextStyle(
                    fontSize: 12,
                    color: StyleRepo.lightGrey,
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              'إغلاق',
              style: TextStyle(color: StyleRepo.blue),
            ),
          ),
        ],
      ),
    );
  }

  // حذف إشعار
  void deleteNotification(String notificationId) {
    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          'حذف الإشعار',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: StyleRepo.black,
          ),
        ),
        content: Text(
          'هل أنت متأكد من حذف هذا الإشعار؟',
          style: TextStyle(color: StyleRepo.darkGrey),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              'إلغاء',
              style: TextStyle(color: StyleRepo.lightGrey),
            ),
          ),
          TextButton(
            onPressed: () {
              notificationService.deleteNotification(notificationId);
              Get.back();
              Get.snackbar(
                'تم الحذف',
                'تم حذف الإشعار بنجاح',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: StyleRepo.green.withOpacity(0.9),
                colorText: StyleRepo.white,
                duration: const Duration(seconds: 2),
              );
            },
            child: Text(
              'حذف',
              style: TextStyle(color: StyleRepo.red),
            ),
          ),
        ],
      ),
    );
  }

  // تحديد جميع الإشعارات كمقروءة
  void markAllAsRead() {
    notificationService.markAllAsRead();
    Get.snackbar(
      'تم التحديث',
      'تم تحديد جميع الإشعارات كمقروءة',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: StyleRepo.blue.withOpacity(0.9),
      colorText: StyleRepo.white,
      duration: const Duration(seconds: 2),
    );
  }

  // عرض حوار مسح جميع الإشعارات
  void showClearAllDialog() {
    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(Icons.warning, color: StyleRepo.orange),
            const SizedBox(width: 8),
            Text(
              'مسح جميع الإشعارات',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: StyleRepo.black,
              ),
            ),
          ],
        ),
        content: Text(
          'هل أنت متأكد من مسح جميع الإشعارات؟ لا يمكن التراجع عن هذا الإجراء.',
          style: TextStyle(color: StyleRepo.darkGrey),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              'إلغاء',
              style: TextStyle(color: StyleRepo.lightGrey),
            ),
          ),
          TextButton(
            onPressed: () {
              notificationService.clearAllNotifications();
              Get.back();
              Get.snackbar(
                'تم المسح',
                'تم مسح جميع الإشعارات بنجاح',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: StyleRepo.red.withOpacity(0.9),
                colorText: StyleRepo.white,
                duration: const Duration(seconds: 2),
              );
            },
            child: Text(
              'مسح الكل',
              style: TextStyle(color: StyleRepo.red),
            ),
          ),
        ],
      ),
    );
  }

  // الحصول على أيقونة الإشعار حسب النوع
  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.orderUpdate:
        return Icons.shopping_bag_outlined;
      case NotificationType.promotion:
        return Icons.local_offer_outlined;
      case NotificationType.general:
      default:
        return Icons.notifications_outlined;
    }
  }

  // الحصول على لون الإشعار حسب النوع
  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.orderUpdate:
        return StyleRepo.blue;
      case NotificationType.promotion:
        return StyleRepo.orange;
      case NotificationType.general:
      default:
        return StyleRepo.indigo;
    }
  }

  // تنسيق وقت الإشعار
  String _formatNotificationTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }
}
