import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/models/product.dart';
import 'package:shop_app/core/services/rest_api/rest_api.dart';
import 'package:shop_app/core/services/state_management/obs.dart';

class CategoriesPageController extends GetxController {
  final TextEditingController searchController = TextEditingController();
  // متغير للتحكم في حالة البحث
  RxBool isSearching = false.obs;
  // متغير لتخزين نص البحث
  RxString searchQuery = ''.obs;
  // قائمة لتخزين نتائج البحث
  ObsList<ProductsModel> searchResults = ObsList([]);

  /// دالة للبحث عن المنتجات من خلال API

  searchProducts(String query) async {
    // تحديث حالة البحث ونص البحث
    isSearching.value = true;
    searchQuery.value = query;

    // إذا كان نص البحث فارغًا، نقوم بإفراغ نتائج البحث والخروج
    if (query.isEmpty) {
      searchResults.value = [];
      isSearching.value = false;
      return;
    }

    try {
      // إرسال طلب البحث إلى API
      ResponseModel response = await APIService.instance.request(
        Request(
          endPoint: EndPoints.products,
          params: {"search": query},
          getdata: "products",
          fromJson: ProductsModel.fromJson,
        ),
      );

      // التحقق من نجاح الطلب
      if (response.success) {
        // تحديث نتائج البحث
        searchResults.value = response.data;
        if (searchResults.value!.isEmpty) {
          searchResults.error = tr(LocaleKeys.No_Results_Found);
        }
      } else {
        searchResults.error = response.message;
      }
    } catch (e) {
      // التعامل مع الأخطاء غير المتوقعة
      searchResults.error = "حدث خطأ أثناء البحث";
    }
  }

  /// دالة لإلغاء البحث والعودة إلى العرض العادي
  clearSearch() {
    isSearching.value = false;
    searchQuery.value = '';
    searchResults.value = [];
    searchController.clear();
  }

  //!SECTION
  @override
  void onClose() {
    // التخلص من متحكم النص عند إغلاق المتحكم
    searchController.dispose();
    super.onClose();
  }
}
